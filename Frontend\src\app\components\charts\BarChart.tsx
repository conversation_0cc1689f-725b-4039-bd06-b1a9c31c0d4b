// import React from "react";
// import { BarChart } from "@mui/x-charts";
// import CustomTooltip from "../../components/charts/CustomTooltip";

// export default function MyBarChart() {
//   // Datos para cada serie
//   const series1 = [2500, 2700, 2300, 2900, 3100];
//   const series2 = [2000, 2200, 1800, 2600, 2100];
//   const series3 = [3000, 2800, 3200, 2000, 2500];

//   // Etiquetas para el eje X
//   const labels = ["Enero", "Febrero", "Marzo", "Abril", "Mayo"];

//   return (
//     <BarChart
//       width={600}
//       height={400}
//       xAxis={[{ scaleType: "band", data: labels, label: "Mes" }]}
//       yAxis={[{ label: "Cantidad" }]}
//       series={[
//         { data: series1, label: "Series 1", color: "#00BFA6" },
//         { data: series2, label: "Series 2", color: "#1E88E5" },
//         { data: series3, label: "Series 3", color: "#9C27B0" },
//       ]}
//       tooltip={{ tooltipComponent: CustomTooltip } as any}
//     />
//   );
// }
