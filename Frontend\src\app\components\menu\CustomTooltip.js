import React from "react";
import { Tooltip, tooltipClasses, styled } from "@mui/material";

// ✨ Tooltip personalizado
const CustomTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} arrow />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#4CAF50", // Fondo naranja
    color: "#FFF", // Texto blanco
    fontSize: "14px", // Tamaño del texto
    borderRadius: "8px", // Bordes redondeados
    boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.4)", // Sombra más fuerte
    borderRadius: "12px", // Bordes más redondeados
    padding: "10px 16px", // Espaciado interno
    fontFamily: "Arial, sans-serif", // Fuente personalizada
    maxWidth: "200px", // Ancho máximo
    transition: "all 0.3s ease-in-out", // Animación
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: "#4CAF50", // El color de la flecha se iguala al fondo
  },
}));

export default CustomTooltip;
