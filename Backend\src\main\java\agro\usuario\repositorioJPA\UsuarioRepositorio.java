package agro.usuario.repositorioJPA;
/*Los repositorios se encargan de la interaccion con la capa de persistencia de datos (generalmente bases de datos)*/

import org.springframework.data.jpa.repository.JpaRepository;

import agro.usuario.modeloEntidades.UsuarioModelo;

import java.util.UUID;

public interface UsuarioRepositorio extends JpaRepository<UsuarioModelo,UUID> {
   
    UsuarioModelo findByMail(String mail);
    boolean existsByMail(String mail);
   
} 

