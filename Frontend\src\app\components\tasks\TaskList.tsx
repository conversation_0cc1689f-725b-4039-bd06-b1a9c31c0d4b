import React, { useEffect, useState } from "react";
import { Box, Typography, Card, Skeleton, Chip, Button } from "@mui/material";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import PendingOutlinedIcon from "@mui/icons-material/PendingOutlined";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import AssignmentIcon from "@mui/icons-material/Assignment";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import Link from "next/link";
import { styled } from "@mui/material/styles";

interface Task {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in-progress" | "completed";
  due_date: string;
}

interface TaskListProps {
  farmId?: string;
  fieldId?: string;
  limit?: number;
}

const StyledContainer = styled(Card)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: "#ffffff",
  borderRadius: "12px",
  border: "1px solid #E5E7EB",
  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.05)",
  marginBottom: theme.spacing(2),
  transition: "all 0.3s ease",
  "&:hover": {
    boxShadow: "0 6px 16px rgba(0, 0, 0, 0.08)",
  },
}));

const HeaderBox = styled(Box)(({ theme }) => ({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  paddingBottom: theme.spacing(2),
  marginBottom: theme.spacing(2),
  borderBottom: "1px solid #eaeaea",
}));

const TaskCard = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: "8px",
  marginBottom: theme.spacing(2),
  transition: "all 0.2s ease",
  backgroundColor: "#f9fafb",
  "&:hover": {
    backgroundColor: "#f0f4ff",
    transform: "translateY(-2px)",
  },
  "&:last-child": {
    marginBottom: 0,
  },
}));

const StatusChip = styled(Chip)(
  ({ theme, status }: { theme: any; status: string }) => {
    const colors = {
      completed: {
        bg: "#e8f5e9",
        color: "#2e7d32",
      },
      "in-progress": {
        bg: "#fff8e1",
        color: "#f57c00",
      },
      pending: {
        bg: "#e3f2fd",
        color: "#1976d2",
      },
    };

    const statusType = status as keyof typeof colors;

    return {
      backgroundColor: colors[statusType].bg,
      color: colors[statusType].color,
      fontFamily: "Inter, sans-serif",
      fontWeight: 500,
      fontSize: "0.75rem",
      height: "24px",
      "& .MuiChip-icon": {
        color: colors[statusType].color,
      },
    };
  }
);

const ViewAllButton = styled(Button)(({ theme }) => ({
  textTransform: "none",
  fontFamily: "Inter, sans-serif",
  fontWeight: 500,
  fontSize: "0.875rem",
  color: theme.palette.primary.main,
  "&:hover": {
    backgroundColor: "rgba(25, 118, 210, 0.04)",
  },
}));

const TaskList: React.FC<TaskListProps> = ({ farmId, fieldId, limit = 3 }) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        // Endpoint no implementado - retornar datos mock temporalmente
        console.warn(
          "⚠️ Endpoint /api/tarea no implementado - retornando array vacío"
        );
        setTasks([]);
      } catch (error) {
        console.error("Error fetching tasks:", error);
        setTasks([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTasks();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircleOutlineIcon sx={{ fontSize: "18px" }} />;
      case "in-progress":
        return <AccessTimeIcon sx={{ fontSize: "18px" }} />;
      default:
        return <PendingOutlinedIcon sx={{ fontSize: "18px" }} />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "completed":
        return "Completado";
      case "in-progress":
        return "En Progreso";
      case "pending":
        return "Pendiente";
      default:
        return "Desconocido";
    }
  };

  const formatDueDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return "Hoy";
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return "Mañana";
    } else {
      return date.toLocaleDateString("es-ES");
    }
  };

  return (
    <StyledContainer>
      <HeaderBox>
        <Typography
          variant="h6"
          component="h2"
          sx={{
            fontFamily: "Lexend, sans-serif",
            fontWeight: 600,
            fontSize: "1.125rem",
            color: "#111827",
          }}
        >
          Resumen de Tareas
        </Typography>
        <Link href="/tareas" passHref>
          <ViewAllButton endIcon={<ArrowForwardIcon />}>
            Ver todas
          </ViewAllButton>
        </Link>
      </HeaderBox>

      <Box>
        {loading ? (
          // Skeleton loading state
          Array.from(new Array(2)).map((_, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Skeleton
                variant="rectangular"
                height={100}
                sx={{ borderRadius: 2 }}
              />
            </Box>
          ))
        ) : tasks.length === 0 ? (
          <Box
            sx={{
              py: 4,
              textAlign: "center",
              backgroundColor: "#f9fafb",
              borderRadius: "8px",
            }}
          >
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ fontFamily: "Inter, sans-serif" }}
            >
              No hay tareas por realizar
            </Typography>
          </Box>
        ) : (
          tasks.slice(0, limit).map((task) => (
            <TaskCard key={task.id}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "flex-start",
                  flexWrap: "wrap",
                  gap: 1,
                }}
              >
                {/* Información principal */}
                <Box sx={{ flex: 1, minWidth: "200px" }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      mb: 1.5,
                    }}
                  >
                    <AssignmentIcon
                      sx={{
                        mr: 1.5,
                        color: "#3b82f6",
                        fontSize: "22px",
                      }}
                    />
                    <Typography
                      variant="subtitle1"
                      sx={{
                        fontWeight: 600,
                        color: "#111827",
                        fontFamily: "Lexend, sans-serif",
                        fontSize: "1rem",
                      }}
                    >
                      {task.title}
                    </Typography>
                  </Box>

                  <Typography
                    variant="body2"
                    sx={{
                      color: "#4b5563",
                      fontFamily: "Inter, sans-serif",
                      ml: 4.5,
                      mb: 1.5,
                      lineHeight: 1.5,
                    }}
                  >
                    {task.description}
                  </Typography>

                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      ml: 4.5,
                    }}
                  >
                    <CalendarTodayIcon
                      sx={{
                        mr: 1,
                        color: "#6b7280",
                        fontSize: "16px",
                      }}
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#6b7280",
                        fontFamily: "Inter, sans-serif",
                        fontWeight: 500,
                        fontSize: "0.8125rem",
                      }}
                    >
                      {formatDueDate(task.due_date)}
                    </Typography>
                  </Box>
                </Box>

                {/* Estado */}
                <StatusChip
                  icon={getStatusIcon(task.status)}
                  label={getStatusLabel(task.status)}
                  size="small"
                  status={task.status}
                  theme={undefined}
                />
              </Box>
            </TaskCard>
          ))
        )}
      </Box>
    </StyledContainer>
  );
};

export default TaskList;
