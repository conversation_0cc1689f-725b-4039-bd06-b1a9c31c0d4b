import React, { useState } from "react";
import Map, { NavigationControl } from "react-map-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";

const MapViews = () => {
    
  const [mapStyle, setMapStyle] = useState( "mapbox://styles/mapbox/streets-v11" );
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const open = Boolean(anchorEl);

  // Lista de estilos disponibles
  const styles = [
    { label: "Streets", value: "mapbox://styles/mapbox/streets-v11" },
    { label: "Satellite", value: "mapbox://styles/mapbox/satellite-v9" },
    { label: "Outdoors", value: "mapbox://styles/mapbox/outdoors-v11" },
    { label: "Dark", value: "mapbox://styles/mapbox/dark-v10" },
    { label: "Light", value: "mapbox://styles/mapbox/light-v10" },
    { label: "Navigation Day",value: "mapbox://styles/mapbox/navigation-day-v1",},
    { label: "Navigation Night", value: "mapbox://styles/mapbox/navigation-night-v1",},
  ];

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = (style: string) => {
    setMapStyle(style);
    setAnchorEl(null);
  };

  return (
    <div>
      {/* Botón para abrir el menú de estilos */}
      <Button
        aria-controls="style-menu"
        aria-haspopup="true"
        onClick={handleClick}
        variant="contained"
        color="primary"
      >
        Select Map Style
      </Button>

      {/* Menú desplegable con las opciones de estilos */}
      <Menu
        id="style-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={() => handleClose(mapStyle)}
      >
        {styles.map((style) => (
          <MenuItem key={style.value} onClick={() => handleClose(style.value)}>
            {style.label}
          </MenuItem>
        ))}
      </Menu>

      {/* Componente Mapbox */}
      <Map
        initialViewState={{
          latitude: -34.6037, // Ubicación inicial
          longitude: -58.3816,
          zoom: 10,
        }}
        style={{ width: "100%", height: "500px" }}
        mapStyle={mapStyle}
        mapboxAccessToken="pk.eyJ1IjoicGVwZW1hcGJveDg2IiwiYSI6ImNtMHBoYzRsbzAxNGIycnBza2RzbmRudHQifQ.440E50Y_qT002C9sFQWm5A"
      >
        <NavigationControl position="top-right" />
      </Map>
    </div>
  );
};

export default MapViews;
