import React, { useEffect, useRef, useState, useCallback } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import "@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css";
import {
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import * as turf from "@turf/turf";
import { Box } from "@mui/system";
import MapIcon from "@mui/icons-material/Map";

interface Point {
  latitude: number;
  longitude: number;
}

interface MapDialogData {
  name: string;
  points: Point[];
  areaInHectares: number;
}

interface Lote {
  id: number;
  name: string;
  coordinates: number[][];
  area: number;
  parcels: any[];
}

interface MapDialogProps {
  openDialog: boolean;
  onClose: () => void;
  onSave: (data: MapDialogData) => void;
  establecimientoId: string;
  lotes?: Lote[];
  selectedLoteId?: number | null;
}

const styles = `
  .mapboxgl-ctrl-custom {
    background: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    height: 29px;
    width: 29px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mapboxgl-ctrl-custom:hover {
    background-color: #f2f2f2;
  }
`;

const MapDialog: React.FC<MapDialogProps> = ({
  openDialog,
  onClose,
  onSave,
  establecimientoId,
  lotes = [],
  selectedLoteId = null,
}) => {
  const [isMaximized, setIsMaximized] = useState(false);
  const [mapStyle, setMapStyle] = useState(
    "mapbox://styles/mapbox/streets-v11"
  );
  const mapContainerRef = useRef(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const markersRef = useRef<mapboxgl.Marker[]>([]);
  const [points, setPoints] = useState<Point[]>([]);
  const [areaInHectares, setAreaInHectares] = useState("");
  const [allLotes, setAllLotes] = useState([]);
  const [isParcelas, setIsParcelas] = useState(false);
  const [error, setError] = useState<{ [key: string]: string }>({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [nombre, setNombre] = useState("");
  const [open, setOpen] = useState(false);

  // Al inicio del componente MapDialog, añade este log
  useEffect(() => {
    console.log("MapDialog montado con lotes:", lotes);

    // Inspeccionar el formato de las coordenadas
    if (lotes && lotes.length > 0) {
      lotes.forEach((lote) => {
        console.log(
          `Lote ${lote.id} (${lote.name}) coordenadas:`,
          lote.coordinates
        );
      });
    }
  }, [lotes]);

  // Calcula el área del polígono usando turf y actualiza el estado
  const obtenrDatosSuperficie = useCallback(() => {
    if (points.length === 4) {
      const polygonCoords = points.map((p) => [p.longitude, p.latitude]);
      // Cerrar el polígono agregando el primer punto al final
      polygonCoords.push(polygonCoords[0]);
      const polygonGeoJSON = {
        type: "Feature",
        properties: {},
        geometry: { type: "Polygon", coordinates: [polygonCoords] },
      };
      const areaInSquareMeters = turf.area(polygonGeoJSON as turf.AllGeoJSON);
      const areaHectaresCalc = areaInSquareMeters / 10000;
      setAreaInHectares(areaHectaresCalc.toFixed(2));
    } else {
      setAreaInHectares("");
    }
  }, [points]);

  // Captura el clic en el mapa y añade el punto (máximo 4)
  const handleMapClick = useCallback((e) => {
    setPoints((prevPoints) => {
      if (prevPoints.length >= 4) return prevPoints;
      return [
        ...prevPoints,
        { longitude: e.lngLat.lng, latitude: e.lngLat.lat },
      ];
    });
  }, []);

  // Función para encontrar la ubicación actual
  const handleFindLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        const { latitude, longitude } = position.coords;
        if (mapRef.current) {
          mapRef.current.flyTo({ center: [longitude, latitude], zoom: 14 });
        }
      });
    }
  };

  // Función para eliminar puntos (resetear)
  const handleDelete = () => {
    setPoints([]);
  };

  // Función para cambiar el estilo del mapa
  const toggleMapStyle = () => {
    const mapStyles = [
      { value: "mapbox://styles/mapbox/streets-v11", label: "Calles" },
      { value: "mapbox://styles/mapbox/satellite-v9", label: "Satélite" },
      { value: "mapbox://styles/mapbox/light-v10", label: "Luz" },
      { value: "mapbox://styles/mapbox/dark-v10", label: "Oscuro" },
      { value: "mapbox://styles/mapbox/outdoors-v11", label: "Afuera" },
    ];
    const currentIndex = mapStyles.findIndex(
      (style) => style.value === mapStyle
    );
    const nextIndex = (currentIndex + 1) % mapStyles.length;
    setMapStyle(mapStyles[nextIndex].value);
  };

  const mapStyles = [
    {
      value: "mapbox://styles/mapbox/streets-v11",
      label: "Calles",
      icon: "🗺️", // Puedes cambiar el icono con un emoji
    },
    {
      value: "mapbox://styles/mapbox/satellite-v9",
      label: "Satélite",
      icon: "🛰️", // Emoji de satélite
    },
    {
      value: "mapbox://styles/mapbox/light-v10",
      label: "Luz",
      icon: "☀️", // Emoji de sol
    },
    {
      value: "mapbox://styles/mapbox/dark-v10",
      label: "Oscuro",
      icon: "🌙", // Emoji de luna
    },
    {
      value: "mapbox://styles/mapbox/outdoors-v11",
      label: "Afuera",
      icon: "🏞️", // Emoji de paisaje
    },
  ];

  // Función para generar colores únicos automáticamente basados en el ID del establecimiento
  const getColorForEstablecimiento = useCallback(
    (establecimientoId: string) => {
      // Convertir el ID a un número hash para usar como semilla
      let hash = 0;
      for (let i = 0; i < establecimientoId.length; i++) {
        const char = establecimientoId.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convertir a entero de 32 bits
      }

      // Usar el hash para generar valores HSL (Hue, Saturation, Lightness)
      // Esto garantiza colores visualmente distintos y vibrantes
      const hue = Math.abs(hash) % 360; // Matiz: 0-360 grados
      const saturation = 65 + (Math.abs(hash >> 8) % 25); // Saturación: 65-90%
      const lightness = 45 + (Math.abs(hash >> 16) % 15); // Luminosidad: 45-60%

      const color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;

      // Log para debug (opcional, puedes removerlo después)
      console.log(
        `🎨 Color generado para establecimiento ${establecimientoId}: ${color} (H:${hue}, S:${saturation}%, L:${lightness}%)`
      );

      return color;
    },
    []
  );

  // Función auxiliar para crear un elemento de etiqueta
  const createLabelElement = useCallback((name: string, area: number) => {
    const el = document.createElement("div");
    el.className = "lote-label";
    el.style.backgroundColor = "rgba(255, 255, 255, 0.9)";
    el.style.border = "none";
    el.style.borderRadius = "3px";
    el.style.padding = "2px 6px";
    el.style.fontSize = "11px";
    el.style.fontWeight = "600";
    el.style.whiteSpace = "nowrap";
    el.style.pointerEvents = "none";
    el.style.color = "#333";
    el.style.boxShadow = "0 1px 3px rgba(0,0,0,0.3)";
    el.style.fontFamily = "Arial, sans-serif";
    el.innerHTML = `${name} (${area.toFixed(2)} ha)`;
    return el;
  }, []);

  // Función auxiliar para obtener el centroide de un polígono
  const getCentroid = useCallback((coordinates: any): [number, number] => {
    // Parse coordinates if they're a string
    let coordinatesArray = coordinates;
    if (!Array.isArray(coordinatesArray)) {
      try {
        coordinatesArray = JSON.parse(coordinates);
      } catch (e) {
        console.error("Error al parsear coordenadas para centroide:", e);
        return [-61.9625, -30.5351]; // Default to Suardi coordinates
      }
    }

    // Ensure we have a valid array structure
    if (!Array.isArray(coordinatesArray) || coordinatesArray.length === 0) {
      console.error("Coordenadas inválidas para centroide:", coordinatesArray);
      return [-61.9625, -30.5351]; // Default to Suardi coordinates
    }

    // Handle different coordinate structures
    let flatCoords = coordinatesArray;

    // If it's a nested array (like from GeoJSON), flatten it
    if (
      Array.isArray(coordinatesArray[0]) &&
      Array.isArray(coordinatesArray[0][0])
    ) {
      flatCoords = coordinatesArray[0]; // Take the first ring of the polygon
    }

    if (!Array.isArray(flatCoords) || flatCoords.length === 0) {
      console.error(
        "No se pudieron procesar las coordenadas:",
        coordinatesArray
      );
      return [-61.9625, -30.5351]; // Default to Suardi coordinates
    }

    // Calculate centroid
    let totalLng = 0;
    let totalLat = 0;
    let validPoints = 0;

    flatCoords.forEach((coord: any) => {
      if (Array.isArray(coord) && coord.length >= 2) {
        const lng = parseFloat(coord[0]);
        const lat = parseFloat(coord[1]);
        if (!isNaN(lng) && !isNaN(lat)) {
          totalLng += lng;
          totalLat += lat;
          validPoints++;
        }
      }
    });

    if (validPoints === 0) {
      console.error("No se encontraron puntos válidos para el centroide");
      return [-61.9625, -30.5351]; // Default to Suardi coordinates
    }

    return [totalLng / validPoints, totalLat / validPoints];
  }, []);

  // Función para inicializar el mapa (extraída para evitar duplicación)
  const initializeMap = useCallback(
    (center: [number, number]) => {
      console.log("Inicializando mapa en:", center);
      const map = new mapboxgl.Map({
        container: mapContainerRef.current!,
        style: mapStyle,
        center: center,
        zoom: 14, // Zoom más cercano para mejor visualización
        projection: "mercator", // Prueba con 'globe' o 'mercator'
      });
      mapRef.current = map;

      // Agrega el listener para capturar clics
      map.on("click", handleMapClick);
      map.on("load", () => {
        console.log("Mapa cargado en coordenadas:", center);

        // Agregar controles básicos
        map.addControl(new mapboxgl.NavigationControl(), "top-right");
        map.addControl(new mapboxgl.FullscreenControl(), "top-left");

        // Añadir un marcador en la ubicación actual
        new mapboxgl.Marker({ color: "#FF0000" }).setLngLat(center).addTo(map);

        // Agregar botón personalizado para ir a Suardi
        class SuardiButton {
          _map: mapboxgl.Map | undefined;
          _container: HTMLElement | undefined;

          onAdd(map: mapboxgl.Map) {
            this._map = map;
            this._container = document.createElement("div");
            this._container.className = "mapboxgl-ctrl mapboxgl-ctrl-group";

            const button = document.createElement("button");
            button.className = "mapboxgl-ctrl-custom";
            button.type = "button";
            button.title = "Ir a Suardi";
            button.innerHTML = '<span style="font-size: 18px;">🏠</span>'; // Emoji de casa
            button.onclick = () => {
              const suardiCoords: [number, number] = [-61.9625, -30.5351]; // Suardi, Santa Fe
              map.flyTo({
                center: suardiCoords,
                zoom: 14,
                essential: true,
              });
            };

            this._container.appendChild(button);
            return this._container;
          }

          onRemove() {
            if (this._container && this._container.parentNode) {
              this._container.parentNode.removeChild(this._container);
            }
          }
        }

        // Agregar el botón personalizado al mapa
        map.addControl(new SuardiButton(), "top-right");

        // Dibujar lotes existentes
        if (lotes && Array.isArray(lotes) && lotes.length > 0) {
          console.log("Dibujando lotes existentes:", lotes);

          lotes.forEach((lote) => {
            if (lote.coordinates) {
              console.log(
                `Dibujando lote ${lote.id} (${lote.name}):`,
                lote.coordinates
              );

              // Parse coordinates if they're a string
              let coordinatesArray = lote.coordinates;
              if (!Array.isArray(coordinatesArray)) {
                try {
                  // Try to parse if it's a JSON string
                  coordinatesArray =
                    typeof lote.coordinates === "string"
                      ? JSON.parse(lote.coordinates)
                      : lote.coordinates;
                  console.log("Coordenadas parseadas:", coordinatesArray);
                } catch (e) {
                  console.error(
                    `Error al parsear coordenadas del lote ${lote.id}:`,
                    e
                  );
                  return; // Skip this lote if coordinates can't be parsed
                }
              }

              // Ensure coordinates is an array
              if (
                !Array.isArray(coordinatesArray) ||
                coordinatesArray.length === 0
              ) {
                console.warn(
                  `El lote ${lote.id} (${lote.name}) no tiene coordenadas válidas`
                );
                return;
              }

              // Create a unique ID for each source and layer
              const sourceId = `lote-source-${lote.id}`;
              const fillLayerId = `lote-fill-${lote.id}`;
              const lineLayerId = `lote-line-${lote.id}`;

              try {
                // Verify and correct the format of coordinates if necessary
                // GeoJSON expects coordinates in [longitude, latitude] format
                const correctedCoordinates = coordinatesArray.map((coord) => {
                  // If coordinates appear to be in [latitude, longitude] format
                  // Latitude is typically between -90 and 90, longitude between -180 and 180
                  // For Argentina: lat around -30, lng around -61
                  if (
                    Array.isArray(coord) &&
                    coord.length >= 2 &&
                    Math.abs(coord[0]) <= 90 && // First value looks like latitude
                    Math.abs(coord[1]) <= 180 // Second value looks like longitude
                  ) {
                    // Check if first coordinate is likely latitude (smaller absolute value for Argentina)
                    if (Math.abs(coord[0]) < Math.abs(coord[1])) {
                      console.log(
                        "🔄 Invirtiendo coordenadas [lat,lng] -> [lng,lat]:",
                        coord,
                        "->",
                        [coord[1], coord[0]]
                      );
                      return [coord[1], coord[0]]; // Invert to [longitude, latitude]
                    }
                  }
                  console.log(
                    "✅ Coordenadas ya en formato correcto [lng,lat]:",
                    coord
                  );
                  return coord; // Already in [longitude, latitude] format
                });

                // Verify that there are at least 3 points to form a polygon
                if (correctedCoordinates.length < 3) {
                  console.warn(
                    `El lote ${lote.id} (${lote.name}) no tiene suficientes puntos para formar un polígono`
                  );
                  return;
                }

                // Ensure the polygon is closed (first point = last point)
                const closedCoordinates = [...correctedCoordinates];
                if (
                  closedCoordinates[0][0] !==
                    closedCoordinates[closedCoordinates.length - 1][0] ||
                  closedCoordinates[0][1] !==
                    closedCoordinates[closedCoordinates.length - 1][1]
                ) {
                  closedCoordinates.push(closedCoordinates[0]);
                  console.log(`🔄 Cerrando polígono para lote ${lote.id}`);
                }

                console.log(
                  `📍 Coordenadas finales para lote ${lote.id}:`,
                  closedCoordinates
                );

                // Create a GeoJSON for the lot
                const loteGeoJSON = {
                  type: "Feature",
                  properties: {
                    id: lote.id,
                    name: lote.name,
                    area: lote.area,
                  },
                  geometry: {
                    type: "Polygon",
                    coordinates: [closedCoordinates],
                  },
                };

                console.log(
                  `🗺️ GeoJSON creado para lote ${lote.id}:`,
                  JSON.stringify(loteGeoJSON, null, 2)
                );

                // Add the source to the map
                console.log(`📊 Agregando source ${sourceId} al mapa`);
                map.addSource(sourceId, {
                  type: "geojson",
                  data: loteGeoJSON as GeoJSON.Feature,
                });

                // Obtener el color para este establecimiento
                const establecimientoColor =
                  getColorForEstablecimiento(establecimientoId);
                console.log(
                  `🎨 Color para establecimiento ${establecimientoId}: ${establecimientoColor}`
                );

                // Añadir fill layer con el color del establecimiento
                console.log(`🎨 Agregando fill layer ${fillLayerId}`);
                map.addLayer({
                  id: fillLayerId,
                  type: "fill",
                  source: sourceId,
                  layout: {},
                  paint: {
                    "fill-color": establecimientoColor,
                    "fill-opacity": 0.6,
                  },
                });

                // Añadir line layer con el color del establecimiento
                console.log(`📏 Agregando line layer ${lineLayerId}`);
                map.addLayer({
                  id: lineLayerId,
                  type: "line",
                  source: sourceId,
                  layout: {},
                  paint: {
                    "line-color": establecimientoColor,
                    "line-width": 3,
                  },
                });

                // Add label with lot name
                new mapboxgl.Marker({
                  element: createLabelElement(lote.name, lote.area),
                  anchor: "center",
                })
                  .setLngLat(getCentroid(lote.coordinates) as [number, number])
                  .addTo(map);

                console.log(
                  `✅ Lote ${lote.id} (${lote.name}) dibujado exitosamente`
                );
              } catch (error) {
                console.error(
                  `❌ Error al dibujar el lote ${lote.id} (${lote.name}):`,
                  error
                );
              }
            } else {
              console.warn(
                `⚠️ El lote ${lote.id} (${lote.name}) no tiene coordenadas válidas para dibujar`
              );
            }
          });

          // Añadir configuración del terreno después de dibujar todos los lotes
          try {
            map.addSource("mapbox-dem", {
              type: "raster-dem",
              url: "mapbox://mapbox.mapbox-terrain-dem-v1",
              tileSize: 512,
              maxzoom: 14,
            });
            map.setTerrain({ source: "mapbox-dem", exaggeration: 1.0 });
          } catch (terrainError) {
            console.warn("No se pudo configurar el terreno:", terrainError);
          }

          // If there are lots, adjust the view to show them all
          if (lotes.length > 0) {
            try {
              // Create a bounds that includes all lots
              const bounds = new mapboxgl.LngLatBounds();
              let hasValidCoordinates = false;

              lotes.forEach((lote) => {
                // Get coordinates and parse if needed
                let loteCoordinates = lote.coordinates;
                if (!Array.isArray(loteCoordinates)) {
                  try {
                    loteCoordinates =
                      typeof lote.coordinates === "string"
                        ? JSON.parse(lote.coordinates)
                        : lote.coordinates;
                  } catch (e) {
                    console.error(
                      `Error al parsear coordenadas del lote ${lote.id}:`,
                      e
                    );
                    return; // Skip this lote
                  }
                }

                if (
                  Array.isArray(loteCoordinates) &&
                  loteCoordinates.length > 0
                ) {
                  loteCoordinates.forEach((coord) => {
                    // Verify that the coordinates are valid and close to Suardi
                    const correctedCoord =
                      Math.abs(coord[0]) <= 90 ? [coord[1], coord[0]] : coord;

                    // Verify that the coordinates are within a reasonable range near Suardi
                    const suardiLat = -30.5351;
                    const suardiLng = -61.9625;
                    const maxDistance = 2; // Maximum degrees of distance (approximately 200km)

                    if (
                      Math.abs(correctedCoord[1] - suardiLat) < maxDistance &&
                      Math.abs(correctedCoord[0] - suardiLng) < maxDistance
                    ) {
                      bounds.extend(correctedCoord as mapboxgl.LngLatLike);
                      hasValidCoordinates = true;
                    } else {
                      console.warn(
                        "Coordenada ignorada por estar demasiado lejos de Suardi:",
                        correctedCoord
                      );
                    }
                  });
                }
              });

              // Adjust the map to show all valid lots
              if (hasValidCoordinates) {
                map.fitBounds(bounds, {
                  padding: 50,
                  maxZoom: 15,
                });
              } else {
                // If no valid coordinates, center on Suardi
                const suardiCoords: [number, number] = [-61.9625, -30.5351];
                map.flyTo({
                  center: suardiCoords,
                  zoom: 14,
                  essential: true,
                });
              }
            } catch (error) {
              console.error("Error al ajustar la vista del mapa:", error);
              // In case of error, center on Suardi
              const suardiCoords: [number, number] = [-61.9625, -30.5351];
              map.flyTo({
                center: suardiCoords,
                zoom: 14,
                essential: true,
              });
            }
          } else {
            // If no lots, center on Suardi
            const suardiCoords: [number, number] = [-61.9625, -30.5351];
            map.flyTo({
              center: suardiCoords,
              zoom: 14,
              essential: true,
            });
          }
        }
      });
    },
    [
      mapStyle,
      handleMapClick,
      lotes,
      establecimientoId,
      getColorForEstablecimiento,
      createLabelElement,
      getCentroid,
    ]
  );

  // Inicializa el mapa cuando se abre el diálogo
  useEffect(() => {
    if (!openDialog) return;

    // Reinicia datos al abrir el diálogo
    setPoints([]);
    setAreaInHectares("");
    setNombre("");

    // Depuración de lotes
    console.log("MapDialog montado con lotes:", lotes);

    if (lotes && lotes.length > 0) {
      lotes.forEach((lote) => {
        console.log(
          `Lote ${lote.id} (${lote.name}) coordenadas:`,
          lote.coordinates
        );
        // Verificar si las coordenadas son válidas
        if (
          !lote.coordinates ||
          !Array.isArray(lote.coordinates) ||
          lote.coordinates.length === 0
        ) {
          console.warn(
            `Lote ${lote.id} (${lote.name}) no tiene coordenadas válidas`
          );
        }
      });
    } else {
      console.log("No hay lotes para mostrar en el mapa");
    }

    const timer = setTimeout(() => {
      if (mapContainerRef.current && !mapRef.current) {
        mapboxgl.accessToken =
          "pk.eyJ1IjoicGVwZW1hcGJveDg2IiwiYSI6ImNtMHBoYzRsbzAxNGIycnBza2RzbmRudHQifQ.440E50Y_qT002C9sFQWm5A";

        // Inicializar directamente en Suardi, Santa Fe
        const suardiCoords: [number, number] = [-61.9625, -30.5351]; // Suardi, Santa Fe
        console.log("Inicializando mapa en Suardi, Santa Fe");
        initializeMap(suardiCoords);
      }
    }, 500);

    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
      clearTimeout(timer);
    };
  }, [
    openDialog,
    mapStyle,
    lotes,
    handleMapClick,
    establecimientoId,
    initializeMap,
  ]);

  // Efecto adicional para redibujar lotes cuando cambien y el mapa ya esté inicializado
  useEffect(() => {
    if (!mapRef.current || !openDialog) return;

    console.log("Redibujando lotes en mapa existente:", lotes);

    // Limpiar lotes existentes del mapa
    if (mapRef.current.isStyleLoaded()) {
      // Remover todas las capas y fuentes de lotes existentes
      const style = mapRef.current.getStyle();
      const layers = style?.layers || [];
      const sources = style?.sources || {};

      // Remover capas de lotes
      layers.forEach((layer: any) => {
        if (
          layer.id.startsWith("lote-fill-") ||
          layer.id.startsWith("lote-line-")
        ) {
          if (mapRef.current!.getLayer(layer.id)) {
            mapRef.current!.removeLayer(layer.id);
          }
        }
      });

      // Remover fuentes de lotes
      Object.keys(sources).forEach((sourceId) => {
        if (sourceId.startsWith("lote-source-")) {
          if (mapRef.current!.getSource(sourceId)) {
            mapRef.current!.removeSource(sourceId);
          }
        }
      });

      // Redibujar lotes actualizados
      if (lotes && Array.isArray(lotes) && lotes.length > 0) {
        lotes.forEach((lote) => {
          if (lote.coordinates) {
            // Reutilizar la lógica de dibujo de lotes de initializeMap
            let coordinatesArray = lote.coordinates;
            if (!Array.isArray(coordinatesArray)) {
              try {
                coordinatesArray =
                  typeof lote.coordinates === "string"
                    ? JSON.parse(lote.coordinates)
                    : lote.coordinates;
              } catch (e) {
                console.error(
                  `Error al parsear coordenadas del lote ${lote.id}:`,
                  e
                );
                return;
              }
            }

            if (
              !Array.isArray(coordinatesArray) ||
              coordinatesArray.length === 0
            ) {
              return;
            }

            const sourceId = `lote-source-${lote.id}`;
            const fillLayerId = `lote-fill-${lote.id}`;
            const lineLayerId = `lote-line-${lote.id}`;

            try {
              const correctedCoordinates = coordinatesArray.map((coord) => {
                if (
                  Array.isArray(coord) &&
                  coord.length >= 2 &&
                  Math.abs(coord[0]) <= 90 &&
                  Math.abs(coord[1]) <= 180
                ) {
                  return [coord[1], coord[0]];
                }
                return coord;
              });

              if (correctedCoordinates.length < 3) {
                return;
              }

              const loteGeoJSON = {
                type: "Feature",
                properties: {
                  id: lote.id,
                  name: lote.name,
                  area: lote.area,
                },
                geometry: {
                  type: "Polygon",
                  coordinates: [correctedCoordinates],
                },
              };

              mapRef.current!.addSource(sourceId, {
                type: "geojson",
                data: loteGeoJSON as GeoJSON.Feature,
              });

              const establecimientoColor =
                getColorForEstablecimiento(establecimientoId);

              mapRef.current!.addLayer({
                id: fillLayerId,
                type: "fill",
                source: sourceId,
                layout: {},
                paint: {
                  "fill-color": [
                    "case",
                    ["==", ["get", "id"], selectedLoteId],
                    "#ff9900",
                    establecimientoColor,
                  ],
                  "fill-opacity": 0.5,
                },
              });

              mapRef.current!.addLayer({
                id: lineLayerId,
                type: "line",
                source: sourceId,
                layout: {},
                paint: {
                  "line-color": [
                    "case",
                    ["==", ["get", "id"], selectedLoteId],
                    "#ff6600",
                    establecimientoColor,
                  ],
                  "line-width": 2,
                },
              });

              // Agregar etiqueta
              new mapboxgl.Marker({
                element: createLabelElement(lote.name, lote.area),
                anchor: "center",
              })
                .setLngLat(getCentroid(lote.coordinates) as [number, number])
                .addTo(mapRef.current!);
            } catch (error) {
              console.error(
                `Error al redibujar el lote ${lote.id} (${lote.name}):`,
                error
              );
            }
          }
        });
      }
    }
  }, [
    lotes,
    selectedLoteId,
    establecimientoId,
    openDialog,
    createLabelElement,
    getCentroid,
    getColorForEstablecimiento,
  ]);

  // Actualiza marcadores y polígono cuando se actualicen los puntos
  useEffect(() => {
    if (!mapRef.current) return;

    // Properly type the markers array
    markersRef.current.forEach((marker: mapboxgl.Marker) => marker.remove());
    markersRef.current = [];

    // Create a marker for each point
    points.forEach((p) => {
      const marker = new mapboxgl.Marker({
        scale: 0.6,
      })
        .setLngLat([p.longitude, p.latitude])
        .addTo(mapRef.current!); // Use non-null assertion since we checked above

      markersRef.current.push(marker);
    });

    // If there are 4 points, draw the polygon
    if (points.length === 4) {
      const polygonCoords = points.map((p) => [p.longitude, p.latitude]);
      polygonCoords.push(polygonCoords[0]); // Close the polygon
      const polygonGeoJSON: GeoJSON.Feature<GeoJSON.Polygon> = {
        type: "Feature",
        properties: {},
        geometry: {
          type: "Polygon",
          coordinates: [polygonCoords],
        },
      };

      if (mapRef.current.getSource("polygon")) {
        const source = mapRef.current.getSource(
          "polygon"
        ) as mapboxgl.GeoJSONSource;
        source.setData(polygonGeoJSON);
      } else {
        mapRef.current.addSource("polygon", {
          type: "geojson",
          data: polygonGeoJSON,
        });
        mapRef.current.addLayer({
          id: "polygon-fill",
          type: "fill",
          source: "polygon",
          layout: {},
          paint: {
            "fill-color": "#0080ff",
            "fill-opacity": 0.5,
          },
        });
        mapRef.current.addLayer({
          id: "polygon-outline",
          type: "line",
          source: "polygon",
          layout: {},
          paint: {
            "line-color": "#0080ff",
            "line-width": 2,
          },
        });
      }
    } else {
      // Remove polygon if it exists
      if (mapRef.current.getLayer("polygon-fill")) {
        mapRef.current.removeLayer("polygon-fill");
      }
      if (mapRef.current.getLayer("polygon-outline")) {
        mapRef.current.removeLayer("polygon-outline");
      }
      if (mapRef.current.getSource("polygon")) {
        mapRef.current.removeSource("polygon");
      }
    }
  }, [points]);

  // Recalcula el área cada vez que se actualizan los puntos
  useEffect(() => {
    obtenrDatosSuperficie();
  }, [points, obtenrDatosSuperficie]);

  // Genera el string con todas las coordenadas, separándolas con " | "
  const coordinatesString = points
    .map((p) => `${p.longitude.toFixed(6)}, ${p.latitude.toFixed(6)}`)
    .join(" | ");

  // Guarda los datos y resetea el estado
  const handleLoadValues = () => {
    if (points.length >= 4) {
      obtenrDatosSuperficie();

      const data = {
        points: points.map((p) => ({
          latitude: p.latitude,
          longitude: p.longitude,
        })),
        areaInHectares: Number(areaInHectares),
        name: nombre,
      };

      onSave(data); // Pass data to parent component without showing alert
      onClose();
    } else {
      // Optional: Add error handling for incomplete polygon
      alert("Por favor, marque 4 puntos en el mapa para definir el lote.");
    }
  };

  // Añade un manejador de cambios para el TextField
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNombre(e.target.value);
  };

  return (
    <Dialog
      open={openDialog}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        "& .MuiPaper-root": {
          transition: "width 0.3s ease, height 0.3s ease",
          width: isMaximized ? "100%" : "auto",
          height: isMaximized ? "100%" : "auto",
          maxWidth: isMaximized ? "100%" : "900px", // Reducido de 1200px a 900px
          maxHeight: isMaximized ? "100%" : "800px",
        },
      }}
    >
      <DialogTitle>
        Marcar Coordenadas
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ position: "absolute", right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Box
          sx={{
            backgroundColor: "#f0f4f8",
            padding: "20px",
            borderRadius: "8px",
            boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
            display: "flex",
            alignItems: "center",
            gap: "10px",
            marginTop: "15px",
            marginBottom: "15px",
          }}
        >
          <MapIcon sx={{ color: "#5C6BC0", fontSize: "30px" }} />
          <Typography
            variant="body1"
            sx={{ color: "#555", fontSize: "16px", fontWeight: 400 }}
          >
            Marque 4 puntos en el mapa para definir el lote y/o parcela. Ingrese
            un nombre y el sistema calculará automáticamente el área. Use el
            botón superior derecho para cambiar la vista.
          </Typography>
        </Box>

        <div
          ref={mapContainerRef}
          style={{
            position: "relative",
            height: "500px",
            border: "2px solid black",
          }}
        ></div>

        {/* Panel para mostrar las coordenadas y la superficie */}
        <Box mt={2} display="flex" flexDirection="column" gap={2}>
          <TextField
            label="Nombre del Lote"
            variant="outlined"
            value={nombre}
            onChange={handleNameChange}
            InputProps={{
              sx: { fontSize: "0.875rem" },
            }}
            sx={{ width: "300px" }}
          />
          <Box display="flex" gap={2}>
            <TextField
              label="Coordenadas"
              value={coordinatesString}
              InputProps={{ readOnly: true }}
              disabled
              sx={{ flex: 3 }}
            />
            <TextField
              label="Superficie (ha)"
              value={areaInHectares ? `${areaInHectares} ha` : ""}
              InputProps={{ readOnly: true }}
              disabled
              sx={{ flex: 1 }}
            />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleLoadValues} color="primary">
          Cargar Valores
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MapDialog;
