
package agro.establecimientos;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import agro.establecimientos.dto.EstablecimientoDto;
import agro.establecimientos.modeloEntidades.EstablecimientoEntidad;
import agro.establecimientos.repositorioJPA.EstablecimientoRepositorio;
import agro.lotes.dto.LoteDto;
import agro.lotes.modeloEntidades.LoteEntidad;
import agro.personas.dto.PersonaDto;
import agro.personas.modeloEntidades.PersonaEntidad;
import agro.personas.repositorioJPA.PersonaRepositorio;
import lombok.RequiredArgsConstructor;


@Service
@RequiredArgsConstructor
public class EstablecimientoCampo {
    private final EstablecimientoRepositorio establecimientoRepositorio;
    private final PersonaRepositorio personaRepositorio;

    /* Guardar Establecimiento */
    public EstablecimientoEntidad guardarEstablecimiento(EstablecimientoDto establecimientoNuevo) {
        if (establecimientoNuevo == null) {
            throw new IllegalArgumentException("El establecimiento proporcionado es nulo.");
        }

        System.out.println("Validando establecimiento...");
        if (!establecimientoNuevo.isValid()) {
            throw new IllegalArgumentException(establecimientoNuevo.getValidationErrors());
        }

        // Si tiene ID, es una actualización y debemos usar el método de actualización
        if (establecimientoNuevo.getId() != null) {
            System.out.println("El establecimiento tiene ID, redirigiendo a método de actualización");
            return actualizarEstablecimiento(establecimientoNuevo);
        }

        String nombreString = establecimientoNuevo.getNombre();
        System.out.println("Verificando si existe establecimiento con nombre: " + nombreString);
        if (establecimientoRepositorio.existsByNombre(nombreString)) {
            throw new IllegalArgumentException("Ya existe un establecimiento con ese nombre.");
        }

        // Crear la entidad establecimiento y asignar los datos básicos
        System.out.println("Creando entidad establecimiento...");
        EstablecimientoEntidad establecimientoModelo = new EstablecimientoEntidad();
        establecimientoModelo.setNombre(establecimientoNuevo.getNombre());
        establecimientoModelo.setLugar(establecimientoNuevo.getLugar());

        // Buscar la persona en base al ID proporcionado
        UUID personaId = establecimientoNuevo.getPersona_id();
        System.out.println("Buscando persona con ID: " + personaId);
        Optional<PersonaEntidad> personaOpcional = personaRepositorio.findById(personaId);
        if (!personaOpcional.isPresent()) {
            throw new IllegalArgumentException("No se encontró la persona solicitada.");
        }
        establecimientoModelo.setPersona(personaOpcional.get());

        // Procesar los lotes si existen
        System.out.println("Procesando lotes...");
        if (establecimientoNuevo.getLotes() != null && !establecimientoNuevo.getLotes().isEmpty()) {
            System.out.println("Cantidad de lotes a procesar: " + establecimientoNuevo.getLotes().size());
            for (LoteDto loteDto : establecimientoNuevo.getLotes()) {
                System.out.println("Procesando lote: " + loteDto.getNombre());
                LoteEntidad loteEntidad = new LoteEntidad();
                loteEntidad.setNombre(loteDto.getNombre());
                loteEntidad.setCoordenadas(loteDto.getCoordenadas());
                loteEntidad.setSuperficie(loteDto.getSuperficie());
                loteEntidad.setEstablecimiento(establecimientoModelo);
                establecimientoModelo.getLotes().add(loteEntidad);
                System.out.println("Lote procesado y agregado al establecimiento");
            }
        } else {
            System.out.println("No hay lotes para procesar");
        }

        // Al guardar el establecimiento se persisten los lotes asociados gracias a
        // CascadeType.ALL
        System.out.println("Guardando establecimiento en la base de datos...");
        EstablecimientoEntidad savedEstablecimiento = establecimientoRepositorio.save(establecimientoModelo);
        System.out.println("Establecimiento guardado con ID: " + savedEstablecimiento.getId());
        return savedEstablecimiento;
    }

    /* Convertir Entidad a DTO */
    public EstablecimientoDto convertToDto(EstablecimientoEntidad entidad) {
        System.out.println("📌 Convirtiendo entidad: " + entidad.getNombre());
        if (entidad.getPersona() != null) {
            System.out.println("📌 Persona encontrada: " + entidad.getPersona().getRazonSocial());
        } else {
            System.out.println("⚠️ Persona es NULL en la entidad");
        }

        EstablecimientoDto dto = new EstablecimientoDto();
        dto.setId(entidad.getId());
        dto.setNombre(entidad.getNombre());
        dto.setLugar(entidad.getLugar());

        if (entidad.getPersona() != null) {
            dto.setPersona_id(entidad.getPersona().getId());

            PersonaDto personaDto = new PersonaDto();
            personaDto.setId(entidad.getPersona().getId());
            personaDto.setRazonSocial(entidad.getPersona().getRazonSocial());
            dto.setPersona(personaDto);
        } else {
            dto.setPersona(null);
        }

        // Convertir lotes
        if (entidad.getLotes() != null) {
            List<LoteDto> lotesDto = entidad.getLotes().stream().map(lote -> {
                LoteDto loteDto = new LoteDto();
                loteDto.setId(lote.getId());
                loteDto.setNombre(lote.getNombre());
                loteDto.setCoordenadas(lote.getCoordenadas());
                loteDto.setSuperficie(lote.getSuperficie());
                return loteDto;
            }).collect(Collectors.toList());
            dto.setLotes(lotesDto);
        }

        return dto;
    }

    /* Lista de Establecimientos en formato DTO */
    public List<EstablecimientoDto> listaEstablecimiento() {
        return establecimientoRepositorio.findAll().stream().map(this::convertToDto).collect(Collectors.toList());
    }

    /* Actualizar Establecimiento */
    public EstablecimientoEntidad actualizarEstablecimiento(EstablecimientoDto establecimientoActualizado) {
        if (establecimientoActualizado == null) {
            throw new IllegalArgumentException("El establecimiento proporcionado es nulo.");
        }

        if (establecimientoActualizado.getId() == null) {
            throw new IllegalArgumentException("El ID del establecimiento es requerido para actualizar.");
        }

        System.out.println("Buscando establecimiento con ID: " + establecimientoActualizado.getId());
        Optional<EstablecimientoEntidad> establecimientoExistente;
        
        try {
            UUID id = UUID.fromString(establecimientoActualizado.getId().toString());
            establecimientoExistente = establecimientoRepositorio.findById(id);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("El ID del establecimiento no es válido: " + e.getMessage());
        }
        
        if (!establecimientoExistente.isPresent()) {
            throw new IllegalArgumentException("No se encontró el establecimiento con ID: " + establecimientoActualizado.getId());
        }
        
        EstablecimientoEntidad establecimientoModelo = establecimientoExistente.get();
        
        // Actualizar datos básicos
        if (establecimientoActualizado.getNombre() != null && !establecimientoActualizado.getNombre().isEmpty()) {
            // Solo verificar si el nombre ha cambiado y si el nuevo nombre ya existe
            if (!establecimientoModelo.getNombre().equals(establecimientoActualizado.getNombre())) {
                if (establecimientoRepositorio.existsByNombre(establecimientoActualizado.getNombre())) {
                    throw new IllegalArgumentException("Ya existe un establecimiento con ese nombre.");
                }
                establecimientoModelo.setNombre(establecimientoActualizado.getNombre());
            }
        }
        
        if (establecimientoActualizado.getLugar() != null) {
            establecimientoModelo.setLugar(establecimientoActualizado.getLugar());
        }
        
        // Actualizar persona si ha cambiado
        if (establecimientoActualizado.getPersona_id() != null && 
            !establecimientoModelo.getPersona().getId().equals(establecimientoActualizado.getPersona_id())) {
            Optional<PersonaEntidad> personaOpcional = personaRepositorio.findById(establecimientoActualizado.getPersona_id());
            if (!personaOpcional.isPresent()) {
                throw new IllegalArgumentException("No se encontró la persona solicitada.");
            }
            establecimientoModelo.setPersona(personaOpcional.get());
        }
        
        // Actualizar lotes
        if (establecimientoActualizado.getLotes() != null && !establecimientoActualizado.getLotes().isEmpty()) {
            // Eliminar lotes existentes
            establecimientoModelo.getLotes().clear();
            
            // Agregar nuevos lotes
            for (LoteDto loteDto : establecimientoActualizado.getLotes()) {
                LoteEntidad loteEntidad = new LoteEntidad();
                loteEntidad.setNombre(loteDto.getNombre());
                loteEntidad.setCoordenadas(loteDto.getCoordenadas());
                loteEntidad.setSuperficie(loteDto.getSuperficie());
                loteEntidad.setEstablecimiento(establecimientoModelo);
                establecimientoModelo.getLotes().add(loteEntidad);
            }
        }
        
        // Guardar cambios
        System.out.println("Guardando cambios del establecimiento...");
        EstablecimientoEntidad savedEstablecimiento = establecimientoRepositorio.save(establecimientoModelo);
        System.out.println("Establecimiento actualizado con ID: " + savedEstablecimiento.getId());
        return savedEstablecimiento;
    }
}
