"use client";
import React, { useState } from "react";
import Link from "next/link";
import {
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Container,
  InputAdornment,
} from "@mui/material";
import { Mail } from "@mui/icons-material";
import Image from "next/image";

const ForgotPasswordForm = () => {
  const [email, setEmail] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Lógica para enviar el correo
  };

  return (
    <Box sx={{ minHeight: "100vh", width: "100%", position: "relative" }}>
      {/* Background Image */}
      <Box
        sx={{
          position: "fixed",
          inset: 0,
          width: "100%",
          height: "100%",
          zIndex: 0,
          "&::before": {
            content: '""',
            position: "absolute",
            inset: 0,
            background:
              "linear-gradient(to right, rgba(30,64,175,0.4), rgba(22,101,52,0.4))",
            mixBlendMode: "multiply",
            zIndex: 1,
          },
        }}
      >
        <Image
          src="/assets/img/fondo.jpg"
          alt="Farm landscape"
          fill
          sizes="100vw"
          style={{ objectFit: "cover" }}
          quality={100}
          priority
        />
      </Box>

      <Container
        maxWidth="sm"
        sx={{
          position: "relative",
          zIndex: 1,
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 4,
            width: "100%",
            maxWidth: "400px",
            backgroundColor: "white",
            borderRadius: "16px",
          }}
        >
          <Typography
            variant="h5"
            component="h1"
            sx={{
              textAlign: "center",
              mb: 2,
              fontWeight: 600,
              fontSize: "24px",
              color: "#1a1a1a",
              fontFamily: "'Roboto', sans-serif",
            }}
          >
            Recuperar Contraseña
          </Typography>

          <Typography
            variant="body1"
            sx={{
              mb: 3,
              color: "#666",
              textAlign: "center",
              fontSize: "14px",
              fontWeight: 400,
              fontFamily: "'Roboto', sans-serif",
              lineHeight: 1.5,
            }}
          >
            Ingresa tu correo electrónico y te enviaremos instrucciones para
            restablecer tu contraseña.
          </Typography>

          <form onSubmit={handleSubmit}>
            <Typography
              variant="subtitle2"
              sx={{
                mb: 1,
                color: "#1a1a1a",
                fontWeight: 500,
                fontSize: "14px",
                fontFamily: "'Roboto', sans-serif",
              }}
            >
              Correo Electrónico
            </Typography>
            
            <TextField
              fullWidth
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              sx={{
                mb: 3,
                "& .MuiOutlinedInput-root": {
                  backgroundColor: "white",
                  fontFamily: "'Roboto', sans-serif",
                  fontSize: "14px",
                  "& fieldset": {
                    borderColor: "#e0e0e0",
                  },
                  "&:hover fieldset": {
                    borderColor: "#1976d2",
                  },
                },
                "& input::placeholder": {
                  fontFamily: "'Roboto', sans-serif",
                  fontSize: "14px",
                  color: "#666",
                },
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Mail sx={{ color: "#666" }} />
                  </InputAdornment>
                ),
              }}
            />

            <Button
              type="submit"
              variant="contained"
              fullWidth
              sx={{
                mb: 2,
                py: 1.5,
                backgroundColor: "#1976d2",
                "&:hover": {
                  backgroundColor: "#1565c0",
                },
                textTransform: "none",
                fontSize: "14px",
                fontWeight: 500,
                fontFamily: "'Roboto', sans-serif",
                borderRadius: "8px",
              }}
            >
              Enviar instrucciones
            </Button>
          </form>

          <Link 
            href="/auth/login" 
            style={{ 
              textDecoration: "none",
              display: "block",
              textAlign: "center",
            }}
          >
            <Button
              variant="text"
              sx={{
                color: "#1976d2",
                textTransform: "none",
                fontSize: "14px",
                fontWeight: 400,
                fontFamily: "'Roboto', sans-serif",
                "&:hover": {
                  backgroundColor: "transparent",
                  textDecoration: "underline",
                },
              }}
            >
              ← Volver al inicio de sesión
            </Button>
          </Link>
        </Paper>
      </Container>
    </Box>
  );
};

export default ForgotPasswordForm;
