"use client";

import * as React from "react";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import { Inter } from "next/font/google";

const inter = Inter({ subsets: ["latin"] });

// Crear tema personalizado para Material-UI
const theme = createTheme({
  palette: {
    primary: {
      main: "#2E7D32", // Verde principal
      dark: "#1B5E20",
      light: "#4CAF50",
    },
    secondary: {
      main: "#0FB60B",
    },
    background: {
      default: "#F5F5F5",
    },
  },
  typography: {
    fontFamily: inter.style.fontFamily,
  },
});

export function ThemeProviderWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
}
