package agro.lotes.modeloEntidades;

import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonBackReference;

import agro.establecimientos.modeloEntidades.EstablecimientoEntidad;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "lotes")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoteEntidad {

    @Id
    @GeneratedValue
    private UUID id;

    private String nombre;
    
    @Column(columnDefinition = "TEXT")
    private String coordenadas;
    
    private Double superficie;

    @ManyToOne
    @JoinColumn(name = "establecimiento_id")
    @JsonBackReference
    private EstablecimientoEntidad establecimiento;
}