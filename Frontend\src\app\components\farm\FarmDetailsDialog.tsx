import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  Box,
  Typography,
  IconButton,
  Tabs,
  Tab,
  Card,
  CardContent,
  styled,
  Divider,
  Grid,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import LandscapeIcon from "@mui/icons-material/Landscape";
import GridViewIcon from "@mui/icons-material/GridView";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import CropOriginalIcon from "@mui/icons-material/CropOriginal";
import { Lexend, Inter } from "next/font/google";

const lexend = Lexend({ subsets: ["latin"] });
const inter = Inter({ subsets: ["latin"] });

const StyledTab = styled(Tab)({
  textTransform: "none",
  fontWeight: 500,
  [`&.${inter.className}`]: {
    fontSize: "14px",
  },
});

const StyledCard = styled(Card)({
  marginBottom: "16px",
  border: "1px solid #e0e0e0",
  boxShadow: "none",
  borderRadius: "8px",
});

const MetricBox = styled(Box)({
  display: "flex",
  alignItems: "center",
  gap: "8px",
  marginBottom: "16px",
});

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`farm-tabpanel-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

interface FarmDetailsDialogProps {
  open: boolean;
  onClose: () => void;
  establecimiento: any;
}

const FarmDetailsDialog = ({
  open,
  onClose,
  establecimiento,
}: FarmDetailsDialogProps) => {
  const [tabValue, setTabValue] = useState(0);

  // Add console logs to see what data we're receiving
  console.log("FarmDetailsDialog - establecimiento:", establecimiento);
  console.log("FarmDetailsDialog - lotes:", establecimiento.lotes);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Calculate total area from all lotes
  const totalArea = React.useMemo(() => {
    if (!establecimiento.lotes || !Array.isArray(establecimiento.lotes)) {
      return 0;
    }

    // Solo considerar lotes que ya están guardados (con ID que no sea numérico temporal)
    const lotesGuardados = establecimiento.lotes.filter(
      (lote: any) =>
        typeof lote.id === "string" ||
        (typeof lote.id === "number" && lote.id < Date.now() - 60000)
    );

    return lotesGuardados.reduce((total: number, lote: any) => {
      // Check for different property names
      const loteArea = lote.area || lote.superficie;
      // Convert to number if it's a string
      const areaValue =
        typeof loteArea === "string" ? parseFloat(loteArea) : loteArea;
      // Add to total if it's a valid number
      return total + (isNaN(areaValue) ? 0 : areaValue);
    }, 0);
  }, [establecimiento.lotes]);

  // Filtrar lotes temporales (no guardados)
  const lotesGuardados = React.useMemo(() => {
    if (!establecimiento.lotes || !Array.isArray(establecimiento.lotes)) {
      return [];
    }

    return establecimiento.lotes.filter(
      (lote: any) =>
        typeof lote.id === "string" ||
        (typeof lote.id === "number" && lote.id < Date.now() - 60000)
    );
  }, [establecimiento.lotes]);

  const totalParcelas = lotesGuardados.reduce(
    (total: number, lote: any) => total + (lote.parcels?.length || 0),
    0
  );

  // Helper function to parse coordinates if they're stored as a string
  const parseCoordinates = (coords: any): number[][] => {
    console.log("Parsing coordinates:", coords);

    if (!coords) {
      console.log("No coordinates provided");
      return [];
    }

    if (Array.isArray(coords)) {
      console.log("Coordinates are already an array");
      return coords;
    }

    if (typeof coords === "string") {
      console.log("Coordinates are a string, attempting to parse");
      try {
        const parsed = JSON.parse(coords);
        console.log("Successfully parsed coordinates:", parsed);
        return parsed;
      } catch (e) {
        console.error("Error parsing coordinates:", e);
        return [];
      }
    }

    console.log("Coordinates are in an unknown format");
    return [];
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: "12px" },
      }}
    >
      <DialogTitle
        sx={{
          pb: 1,
          display: "flex",
          flexDirection: "column",
          gap: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            className={lexend.className}
            sx={{ fontWeight: 600 }}
          >
            {establecimiento.nombre}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
        <Typography
          variant="body2"
          color="text.secondary"
          className={inter.className}
          sx={{ display: "flex", alignItems: "center", gap: 1 }}
        >
          <LocationOnIcon fontSize="small" />
          {establecimiento.lugar}
        </Typography>
      </DialogTitle>

      <Box sx={{ px: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{
            "& .MuiTabs-indicator": {
              backgroundColor: "#1a73e8",
            },
          }}
        >
          <StyledTab label="Información" className={inter.className} />
          <StyledTab label="Lotes" className={inter.className} />
          <StyledTab label="Parcelas" className={inter.className} />
        </Tabs>
      </Box>

      <Box sx={{ px: 3, pb: 3 }}>
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={2}>
            {/* Información General Card */}
            <Grid item xs={12} md={6}>
              <StyledCard>
                <CardContent>
                  <Typography
                    variant="h6"
                    gutterBottom
                    className={lexend.className}
                    sx={{ fontWeight: 600, mb: 2 }}
                  >
                    Información General
                  </Typography>

                  <MetricBox>
                    <LocationOnIcon sx={{ color: "#e53935" }} />
                    <Typography className={inter.className}>
                      {establecimiento.lugar}
                    </Typography>
                  </MetricBox>

                  <MetricBox>
                    <CropOriginalIcon sx={{ color: "#FFA500" }} />
                    <Typography className={inter.className}>
                      {totalArea.toFixed(2)} hectáreas
                    </Typography>
                  </MetricBox>

                  <MetricBox>
                    <CalendarTodayIcon sx={{ color: "#757575" }} />
                    <Typography className={inter.className}>
                      Registrado: {new Date().toLocaleDateString()}
                    </Typography>
                  </MetricBox>
                </CardContent>
              </StyledCard>
            </Grid>

            {/* Capacidad y Recursos Card */}
            <Grid item xs={12} md={6}>
              <StyledCard>
                <CardContent>
                  <Typography
                    variant="h6"
                    gutterBottom
                    className={lexend.className}
                    sx={{ fontWeight: 600, mb: 2 }}
                  >
                    Capacidad y Recursos
                  </Typography>

                  <MetricBox>
                    <LandscapeIcon sx={{ color: "#4caf50" }} />
                    <Typography className={inter.className}>
                      {establecimiento.lotes &&
                      Array.isArray(establecimiento.lotes)
                        ? establecimiento.lotes.length
                        : 0}{" "}
                      lotes
                    </Typography>
                  </MetricBox>

                  <MetricBox>
                    <GridViewIcon sx={{ color: "#8B4513" }} />
                    <Typography className={inter.className}>
                      {totalParcelas} parcelas
                    </Typography>
                  </MetricBox>
                </CardContent>
              </StyledCard>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {lotesGuardados &&
            lotesGuardados.map((lote: any) => {
              // Debug cada lote
              console.log("Rendering lote:", lote);

              // Check for different property names that might exist
              const loteName = lote.name || lote.nombre;
              const loteArea = lote.area || lote.superficie;
              const loteCoordinates = lote.coordinates || lote.coordenadas;

              return (
                <StyledCard key={lote.id}>
                  <CardContent>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        mb: 2,
                      }}
                    >
                      <Typography
                        variant="h6"
                        className={lexend.className}
                        sx={{ fontWeight: 600 }}
                      >
                        {loteName}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        className={inter.className}
                      >
                        {loteArea} hectáreas
                      </Typography>
                    </Box>

                    <Box
                      sx={{ display: "flex", gap: 2, color: "text.secondary" }}
                    >
                      <Typography variant="body2" className={inter.className}>
                        Coordenadas:{" "}
                        {(() => {
                          console.log(
                            "Lote coordinates before parsing:",
                            loteCoordinates
                          );
                          const coords = parseCoordinates(loteCoordinates);
                          console.log("Parsed coordinates:", coords);
                          return coords.length > 0
                            ? coords
                                .map(
                                  (coord: number[]) =>
                                    `${coord[0].toFixed(6)}, ${coord[1].toFixed(
                                      6
                                    )}`
                                )
                                .join(" | ")
                            : "No disponibles";
                        })()}
                      </Typography>
                    </Box>
                  </CardContent>
                </StyledCard>
              );
            })}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {lotesGuardados.map((lote: any) => (
            <Box key={lote.id}>
              <Typography
                variant="h6"
                gutterBottom
                className={lexend.className}
                sx={{ color: "#1a73e8", mb: 2, fontWeight: 600 }}
              >
                Parcelas: {/* Cambiado de "Lote:" a "Parcelas:" */}
              </Typography>

              {lote.parcels && lote.parcels.length > 0 ? (
                lote.parcels.map((parcela: any) => (
                  <StyledCard key={parcela.id}>
                    <CardContent>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          mb: 2,
                        }}
                      >
                        <Typography
                          variant="h6"
                          className={lexend.className}
                          sx={{ fontWeight: 600 }}
                        >
                          {parcela.name}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          className={inter.className}
                        >
                          {parcela.area} hectáreas
                        </Typography>
                      </Box>

                      <Box
                        sx={{
                          display: "flex",
                          gap: 2,
                          color: "text.secondary",
                        }}
                      >
                        <Typography variant="body2" className={inter.className}>
                          Coordenadas:{" "}
                          {(() => {
                            const coords = parseCoordinates(
                              parcela.coordinates
                            );
                            return coords.length > 0
                              ? coords
                                  .map(
                                    (coord: number[]) =>
                                      `${coord[0].toFixed(
                                        6
                                      )}, ${coord[1].toFixed(6)}`
                                  )
                                  .join(" | ")
                              : "No disponibles";
                          })()}
                        </Typography>
                      </Box>
                    </CardContent>
                  </StyledCard>
                ))
              ) : (
                <Typography
                  color="text.secondary"
                  sx={{ mb: 2 }}
                  className={inter.className}
                >
                  No hay parcelas registradas en este lote.
                </Typography>
              )}
              <Divider sx={{ my: 3 }} />
            </Box>
          ))}
        </TabPanel>
      </Box>
    </Dialog>
  );
};

export default FarmDetailsDialog;

