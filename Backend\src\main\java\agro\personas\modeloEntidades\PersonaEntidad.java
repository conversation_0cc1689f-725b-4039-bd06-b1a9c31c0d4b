
package agro.personas.modeloEntidades;

import java.util.List;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonManagedReference;

import agro.establecimientos.modeloEntidades.EstablecimientoEntidad;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity(name = "agricultor")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PersonaEntidad {

    @Id
    @GeneratedValue
    private UUID id;

    // Datos del Propietario/Persona Física (Pestaña 1)
    private String propietarioNombre;
    private String propietarioDocumento;
    private String propietarioTelefono;
    private String propietarioEmail;

    // Datos de la Empresa/Razón Social (Pestaña 2)
    private String razonSocial;
    private String tipoCliente; // 'Productor', 'Empresa', 'Cooperativa', 'Estancia'
    private String direccion; // empresaDomicilio
    private String empresaLocalidad;
    private String provincia; // empresaProvincia
    private String condFrenteIva; // empresaCondFrenteIva

    // Datos del Contacto/Encargado Principal (Pestaña 3)
    private String nombreContacto;
    private String cargoContacto;
    private String telefono;
    private String mail;

    // Campos calculados/derivados
    private String lugar; // empresaLocalidad + " - " + provincia
    private String documento; // Mantener por compatibilidad

    @OneToMany(mappedBy = "persona", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<EstablecimientoEntidad> establecimientos;

}
