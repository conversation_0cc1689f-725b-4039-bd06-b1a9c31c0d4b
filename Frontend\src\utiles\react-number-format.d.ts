declare module "react-number-format" {
  import * as React from "react";

  interface NumberFormatProps {
    thousandSeparator?: boolean | string;
    decimalSeparator?: string;
    thousandSeparatorAlways?: boolean;
    decimalScale?: number;
    allowNegative?: boolean;
    value?: string | number;
    onValueChange?: (values: {
      value: string;
      formattedValue: string;
      floatValue: number;
    }) => void;
    [key: string]: any;
  }

  const NumberFormat: React.FC<NumberFormatProps>;

  export default NumberFormat;
}
