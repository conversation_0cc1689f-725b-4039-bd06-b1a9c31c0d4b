/*Source--> <PERSON><PERSON> y CallStack , pausar y ir paso a paso para ver funcionamiento de JavaScript*/

"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function Page() {
  const router = useRouter();

  useEffect(() => {
    // Redirigir a la página de autenticación
    router.push("/auth/container");
  }, [router]);

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100vh",
        fontSize: "18px",
      }}
    >
      Cargando...
    </div>
  );
}
