"use client";

import { CustomChart } from "@/app/components/charts/CustomCharts";
import {
  Grid,
  Typography,
  Paper,
  Box,
  ToggleButton,
  ToggleButtonGroup,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  Popover,
  TextField,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  IconButton,
  InputAdornment,
} from "@mui/material";
import React, { useState, useMemo } from "react";
import SearchIcon from "@mui/icons-material/Search";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import DateRange from "@mui/icons-material/DateRange";
import CalendarMonth from "@mui/icons-material/CalendarMonth";

// First, define the interfaces
interface DataPoint {
  name: string;
  value: number;
  label: string;
}

interface MonthData {
  [key: string]: DataPoint[];
}

interface YearData {
  [key: string]: MonthData;
}

interface TemporadaData {
  rendimientos: DataPoint[];
  distribucionCultivos: DataPoint[];
  serviciosMensuales: DataPoint[];
  hectareasTrabajadas: DataPoint[];
}

interface Temporadas {
  [key: string]: TemporadaData;
}

interface DatosCompletos {
  rendimientos: YearData;
  distribucionCultivos: YearData;
  servicios: YearData;
  hectareas: YearData;
  temporadas: Temporadas;
}

const Graficos = () => {
  // Estados para los filtros
  const [periodoVista, setPeriodoVista] = useState("mes");
  const [mesSeleccionado, setMesSeleccionado] = useState("01");
  const [añoSeleccionado, setAñoSeleccionado] = useState(
    String(new Date().getFullYear())
  );
  const [temporadaSeleccionada, setTemporadaSeleccionada] =
    useState("2023-2024");
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [anchorElAño, setAnchorElAño] = useState<HTMLElement | null>(null);
  const [searchTermAño, setSearchTermAño] = useState("");

  // Función para generar las temporadas (desde hace 50 años hasta la actual)
  const generarTemporadas = () => {
    const añoActual = new Date().getFullYear();
    const temporadas = [];
    for (let i = 0; i < 50; i++) {
      const añoInicio = añoActual - i;
      temporadas.push(`${añoInicio}-${añoInicio + 1}`);
    }
    return temporadas;
  };

  // Función para filtrar temporadas basado en la búsqueda
  const temporadasFiltradas = generarTemporadas().filter((temporada) =>
    temporada.includes(searchTerm)
  );

  // Agrupar temporadas por décadas
  const temporadasPorDecada = temporadasFiltradas.reduce((acc, temporada) => {
    const decada = Math.floor(parseInt(temporada.split("-")[0]) / 10) * 10;
    if (!acc[decada]) {
      acc[decada] = [];
    }
    acc[decada].push(temporada);
    return acc;
  }, {} as Record<number, string[]>);

  // Función para generar años (desde hace 50 años hasta el actual)
  const generarAños = () => {
    const añoActual = new Date().getFullYear();
    const años = [];

    // Mostrar 10 años hacia el futuro, incluyendo el actual
    for (let año = añoActual + 10; año >= añoActual; año--) {
      años.push(String(año));
    }
    return años;
  };

  // Función para filtrar años basado en la búsqueda
  const añosFiltrados = generarAños().filter((año) =>
    año.includes(searchTermAño)
  );

  // Agrupar años por décadas
  const añosPorDecada = añosFiltrados.reduce((acc, año) => {
    const decada = Math.floor(parseInt(año) / 10) * 10;
    if (!acc[decada]) {
      acc[decada] = [];
    }
    acc[decada].push(año);
    return acc;
  }, {} as Record<number, string[]>);

  // Función auxiliar para etiquetar los años
  const getEtiquetaAño = (año: number) => {
    const añoActual = new Date().getFullYear();
    if (año === añoActual) return `${año} (Actual)`;
    return `${año} (Planificación)`;
  };

  // Handlers
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleTemporadaSelect = (temporada: string) => {
    setTemporadaSeleccionada(temporada);
    handleClose();
  };

  // Handlers para el selector de año
  const handleClickAño = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElAño(event.currentTarget);
  };

  const handleCloseAño = () => {
    setAnchorElAño(null);
  };

  const handleAñoSelect = (año: string) => {
    setAñoSeleccionado(año);
    handleCloseAño();
  };

  // Datos completos (simulando datos de una API)
  const datosCompletos = {
    rendimientos: {
      "2024": {
        "01": [
          { name: "Lote A", value: 3200, label: "3200 kg/ha" },
          { name: "Lote B", value: 4100, label: "4100 kg/ha" },
          { name: "Lote C", value: 2800, label: "2800 kg/ha" },
          { name: "Lote D", value: 3900, label: "3900 kg/ha" },
        ],
        "02": [
          { name: "Lote A", value: 3400, label: "3400 kg/ha" },
          { name: "Lote B", value: 4300, label: "4300 kg/ha" },
          { name: "Lote C", value: 3000, label: "3000 kg/ha" },
          { name: "Lote D", value: 4100, label: "4100 kg/ha" },
        ],
      },
      "2023": {
        "01": [
          { name: "Lote A", value: 3000, label: "3000 kg/ha" },
          { name: "Lote B", value: 3900, label: "3900 kg/ha" },
          { name: "Lote C", value: 2600, label: "2600 kg/ha" },
          { name: "Lote D", value: 3700, label: "3700 kg/ha" },
        ],
      },
      "2022": {
        "01": [
          { name: "Lote A", value: 2800, label: "2800 kg/ha" },
          { name: "Lote B", value: 3700, label: "3700 kg/ha" },
          { name: "Lote C", value: 2400, label: "2400 kg/ha" },
          { name: "Lote D", value: 3500, label: "3500 kg/ha" },
        ],
      },
    },
    distribucionCultivos: {
      "2024": {
        "01": [
          { name: "Soja", value: 45, label: "45%" },
          { name: "Maíz", value: 30, label: "30%" },
          { name: "Trigo", value: 15, label: "15%" },
          { name: "Girasol", value: 10, label: "10%" },
        ],
      },
      "2023": {
        "01": [
          { name: "Soja", value: 40, label: "40%" },
          { name: "Maíz", value: 35, label: "35%" },
          { name: "Trigo", value: 15, label: "15%" },
          { name: "Girasol", value: 10, label: "10%" },
        ],
      },
      "2022": {
        "01": [
          { name: "Soja", value: 42, label: "42%" },
          { name: "Maíz", value: 33, label: "33%" },
          { name: "Trigo", value: 15, label: "15%" },
          { name: "Girasol", value: 10, label: "10%" },
        ],
      },
    },
    servicios: {
      "2024": {
        "01": [
          { name: "Semana 1", value: 15, label: "15 servicios" },
          { name: "Semana 2", value: 20, label: "20 servicios" },
          { name: "Semana 3", value: 25, label: "25 servicios" },
          { name: "Semana 4", value: 18, label: "18 servicios" },
        ],
      },
      "2023": {
        "01": [
          { name: "Semana 1", value: 12, label: "12 servicios" },
          { name: "Semana 2", value: 18, label: "18 servicios" },
          { name: "Semana 3", value: 22, label: "22 servicios" },
          { name: "Semana 4", value: 16, label: "16 servicios" },
        ],
      },
      "2022": {
        "01": [
          { name: "Semana 1", value: 10, label: "10 servicios" },
          { name: "Semana 2", value: 15, label: "15 servicios" },
          { name: "Semana 3", value: 20, label: "20 servicios" },
          { name: "Semana 4", value: 14, label: "14 servicios" },
        ],
      },
    },
    hectareas: {
      "2024": {
        "01": [
          { name: "Lote A", value: 3000, label: "3000 ha" },
          { name: "Lote B", value: 3500, label: "3500 ha" },
          { name: "Lote C", value: 2800, label: "2800 ha" },
          { name: "Lote D", value: 3300, label: "3300 ha" },
        ],
      },
      "2023": {
        "01": [
          { name: "Lote A", value: 2800, label: "2800 ha" },
          { name: "Lote B", value: 3300, label: "3300 ha" },
          { name: "Lote C", value: 2600, label: "2600 ha" },
          { name: "Lote D", value: 3100, label: "3100 ha" },
        ],
      },
      "2022": {
        "01": [
          { name: "Lote A", value: 2600, label: "2600 ha" },
          { name: "Lote B", value: 3100, label: "3100 ha" },
          { name: "Lote C", value: 2400, label: "2400 ha" },
          { name: "Lote D", value: 2900, label: "2900 ha" },
        ],
      },
    },
    temporadas: {
      "2023-2024": {
        rendimientos: [
          { name: "Lote A", value: 3500, label: "3500 kg/ha" },
          { name: "Lote B", value: 4200, label: "4200 kg/ha" },
          { name: "Lote C", value: 3100, label: "3100 kg/ha" },
          { name: "Lote D", value: 4000, label: "4000 kg/ha" },
        ],
        distribucionCultivos: [
          { name: "Soja", value: 50, label: "50%" },
          { name: "Maíz", value: 25, label: "25%" },
          { name: "Trigo", value: 15, label: "15%" },
          { name: "Girasol", value: 10, label: "10%" },
        ],
        serviciosMensuales: [
          { name: "Semana 1", value: 15, label: "15 servicios" },
          { name: "Semana 2", value: 20, label: "20 servicios" },
          { name: "Semana 3", value: 25, label: "25 servicios" },
          { name: "Semana 4", value: 18, label: "18 servicios" },
        ],
        hectareasTrabajadas: [
          { name: "Lote A", value: 3000, label: "3000 ha" },
          { name: "Lote B", value: 3500, label: "3500 ha" },
          { name: "Lote C", value: 2800, label: "2800 ha" },
          { name: "Lote D", value: 3300, label: "3300 ha" },
        ],
      },
      "2022-2023": {
        rendimientos: [
          { name: "Lote A", value: 3300, label: "3300 kg/ha" },
          { name: "Lote B", value: 4000, label: "4000 kg/ha" },
          { name: "Lote C", value: 2900, label: "2900 kg/ha" },
          { name: "Lote D", value: 3800, label: "3800 kg/ha" },
        ],
        distribucionCultivos: [
          { name: "Soja", value: 45, label: "45%" },
          { name: "Maíz", value: 30, label: "30%" },
          { name: "Trigo", value: 15, label: "15%" },
          { name: "Girasol", value: 10, label: "10%" },
        ],
        serviciosMensuales: [
          { name: "Semana 1", value: 12, label: "12 servicios" },
          { name: "Semana 2", value: 18, label: "18 servicios" },
          { name: "Semana 3", value: 22, label: "22 servicios" },
          { name: "Semana 4", value: 16, label: "16 servicios" },
        ],
        hectareasTrabajadas: [
          { name: "Lote A", value: 2800, label: "2800 ha" },
          { name: "Lote B", value: 3300, label: "3300 ha" },
          { name: "Lote C", value: 2600, label: "2600 ha" },
          { name: "Lote D", value: 3100, label: "3100 ha" },
        ],
      },
      "2021-2022": {
        rendimientos: [
          { name: "Lote A", value: 3100, label: "3100 kg/ha" },
          { name: "Lote B", value: 3800, label: "3800 kg/ha" },
          { name: "Lote C", value: 2700, label: "2700 kg/ha" },
          { name: "Lote D", value: 3600, label: "3600 kg/ha" },
        ],
        distribucionCultivos: [
          { name: "Soja", value: 40, label: "40%" },
          { name: "Maíz", value: 35, label: "35%" },
          { name: "Trigo", value: 15, label: "15%" },
          { name: "Girasol", value: 10, label: "10%" },
        ],
        serviciosMensuales: [
          { name: "Semana 1", value: 10, label: "10 servicios" },
          { name: "Semana 2", value: 15, label: "15 servicios" },
          { name: "Semana 3", value: 20, label: "20 servicios" },
          { name: "Semana 4", value: 14, label: "14 servicios" },
        ],
        hectareasTrabajadas: [
          { name: "Lote A", value: 2600, label: "2600 ha" },
          { name: "Lote B", value: 3100, label: "3100 ha" },
          { name: "Lote C", value: 2400, label: "2400 ha" },
          { name: "Lote D", value: 2900, label: "2900 ha" },
        ],
      },
    },
  };

  // Función para obtener datos según el período seleccionado
  const obtenerDatosFiltrados = useMemo(() => {
    if (periodoVista === "temporada") {
      const temporadaData = (datosCompletos as DatosCompletos).temporadas[
        temporadaSeleccionada
      ];
      return {
        rendimientoPorLote: temporadaData?.rendimientos || [],
        distribucionCultivos: temporadaData?.distribucionCultivos || [],
        serviciosMensuales: temporadaData?.serviciosMensuales || [], // Ahora incluye datos
        hectareasTrabajadas: temporadaData?.hectareasTrabajadas || [], // Ahora incluye datos
      };
    } else if (periodoVista === "mes") {
      return {
        rendimientoPorLote:
          (datosCompletos as DatosCompletos).rendimientos[añoSeleccionado]?.[
            mesSeleccionado
          ] || [],
        distribucionCultivos:
          (datosCompletos as DatosCompletos).distribucionCultivos[
            añoSeleccionado
          ]?.[mesSeleccionado] || [],
        serviciosMensuales:
          (datosCompletos as DatosCompletos).servicios[añoSeleccionado]?.[
            mesSeleccionado
          ] || [],
        hectareasTrabajadas:
          (datosCompletos as DatosCompletos).hectareas[añoSeleccionado]?.[
            mesSeleccionado
          ] || [],
      };
    } else {
      // año
      return {
        rendimientoPorLote:
          (datosCompletos as DatosCompletos).rendimientos[añoSeleccionado]?.[
            "01"
          ] || [],
        distribucionCultivos:
          (datosCompletos as DatosCompletos).distribucionCultivos[
            añoSeleccionado
          ]?.["01"] || [],
        serviciosMensuales:
          (datosCompletos as DatosCompletos).servicios[añoSeleccionado]?.[
            "01"
          ] || [],
        hectareasTrabajadas:
          (datosCompletos as DatosCompletos).hectareas[añoSeleccionado]?.[
            "01"
          ] || [],
      };
    }
  }, [periodoVista, mesSeleccionado, añoSeleccionado, temporadaSeleccionada]);

  // Handler para cambio de periodo
  const handlePeriodoChange = (
    event: React.MouseEvent<HTMLElement>,
    newPeriodo: string
  ) => {
    if (newPeriodo !== null) {
      setPeriodoVista(newPeriodo);
    }
  };

  return (
    <>
      <Box
        sx={{
          p: 3, // Añadimos padding general
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            mb: 3,
            mt: 3,
          }}
        >
          <Box>
            <Typography
              variant="h4"
              component="div"
              sx={{ fontWeight: "bold", color: "#2E7D32" }}
            >
              Panel de Análisis Agrícola
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                color: "text.secondary",
                mt: 1,
              }}
            >
              Visualización de métricas y tendencias de su actividad
              agropecuaria
            </Typography>
          </Box>
        </Box>

        {/* Sección de filtros mejorada */}
        <Paper
          elevation={2}
          sx={{
            mb: 4,
            p: 3,
            backgroundColor: "#ffffff",
            borderRadius: "12px",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", md: "row" },
              alignItems: { xs: "stretch", md: "center" },
              gap: 3,
            }}
          >
            {/* Título de la sección */}
            <Typography
              variant="h6"
              sx={{
                minWidth: "140px",
                color: "#1976d2",
                fontWeight: 600,
              }}
            >
              Período de Análisis
            </Typography>

            {/* Contenedor de los filtros */}
            <Box
              sx={{
                display: "flex",
                flexWrap: "wrap",
                gap: 2,
                flex: 1,
                alignItems: "center",
              }}
            >
              {/* Toggle Button Group con estilo mejorado */}
              <ToggleButtonGroup
                value={periodoVista}
                exclusive
                onChange={handlePeriodoChange}
                aria-label="periodo de visualización"
                sx={{
                  "& .MuiToggleButton-root": {
                    textTransform: "none",
                    px: 3,
                    "&.Mui-selected": {
                      backgroundColor: "#1976d2",
                      color: "white",
                      "&:hover": {
                        backgroundColor: "#1565c0",
                      },
                    },
                  },
                }}
              >
                <ToggleButton value="mes">
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <CalendarTodayIcon sx={{ fontSize: 20 }} />
                    <span>Mes</span>
                  </Box>
                </ToggleButton>
                <ToggleButton value="año">
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <DateRange sx={{ fontSize: 20 }} />
                    <span>Año</span>
                  </Box>
                </ToggleButton>
                <ToggleButton value="temporada">
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <CalendarMonth sx={{ fontSize: 20 }} />
                    <span>Temporada</span>
                  </Box>
                </ToggleButton>
              </ToggleButtonGroup>

              {/* Selectores con estilo mejorado */}
              <Box
                sx={{
                  display: "flex",
                  gap: 2,
                  flexWrap: "wrap",
                }}
              >
                {periodoVista === "mes" && (
                  <Box sx={{ display: "inline-flex", alignItems: "center" }}>
                    <IconButton
                      size="small"
                      onClick={() => {
                        const mesNum = parseInt(mesSeleccionado);
                        if (mesNum > 1) {
                          setMesSeleccionado(
                            String(mesNum - 1).padStart(2, "0")
                          );
                        } else {
                          setMesSeleccionado("12");
                        }
                      }}
                    >
                      <ChevronLeftIcon />
                    </IconButton>
                    <Typography
                      sx={{
                        mx: 1,
                        minWidth: "100px",
                        textAlign: "center",
                        fontWeight: 500,
                      }}
                    >
                      {new Date(2000, parseInt(mesSeleccionado) - 1)
                        .toLocaleString("es-ES", { month: "long" })
                        .replace(/^\w/, (c) => c.toUpperCase())}
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={() => {
                        const mesNum = parseInt(mesSeleccionado);
                        if (mesNum < 12) {
                          setMesSeleccionado(
                            String(mesNum + 1).padStart(2, "0")
                          );
                        } else {
                          setMesSeleccionado("01");
                        }
                      }}
                    >
                      <ChevronRightIcon />
                    </IconButton>
                  </Box>
                )}

                {(periodoVista === "mes" || periodoVista === "año") && (
                  <>
                    <TextField
                      size="small"
                      value={añoSeleccionado}
                      onClick={handleClickAño}
                      InputProps={{
                        readOnly: true,
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarTodayIcon sx={{ color: "#1976d2" }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        minWidth: 120,
                        "& .MuiOutlinedInput-root": {
                          borderRadius: "8px",
                          cursor: "pointer",
                          "&:hover": {
                            borderColor: "#1976d2",
                          },
                        },
                      }}
                    />
                    <Popover
                      open={Boolean(anchorElAño)}
                      anchorEl={anchorElAño}
                      onClose={handleCloseAño}
                      anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "left",
                      }}
                      transformOrigin={{
                        vertical: "top",
                        horizontal: "left",
                      }}
                      PaperProps={{
                        sx: {
                          width: 250,
                          maxHeight: 400,
                        },
                      }}
                    >
                      <Box sx={{ p: 2 }}>
                        <TextField
                          fullWidth
                          size="small"
                          placeholder="Buscar año..."
                          value={searchTermAño}
                          onChange={(e) => setSearchTermAño(e.target.value)}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <SearchIcon />
                              </InputAdornment>
                            ),
                          }}
                          sx={{ mb: 2 }}
                        />
                        <List sx={{ maxHeight: 300, overflow: "auto" }}>
                          {Object.entries(añosPorDecada)
                            .sort(
                              ([decadaA], [decadaB]) =>
                                Number(decadaB) - Number(decadaA)
                            )
                            .map(([decada, años]) => (
                              <Box key={decada}>
                                <ListItem sx={{ bgcolor: "#f5f5f5", py: 0.5 }}>
                                  <ListItemText
                                    primary={`Década ${decada}`}
                                    primaryTypographyProps={{
                                      fontWeight: "bold",
                                      fontSize: "0.9rem",
                                      color: "#666",
                                    }}
                                  />
                                </ListItem>
                                {años.map((año) => (
                                  <ListItemButton
                                    key={año}
                                    onClick={() => handleAñoSelect(año)}
                                    selected={año === añoSeleccionado}
                                    sx={{
                                      pl: 3,
                                      "&.Mui-selected": {
                                        bgcolor: "#e3f2fd",
                                        "&:hover": {
                                          bgcolor: "#bbdefb",
                                        },
                                      },
                                      // Estilo especial para años futuros
                                      ...(parseInt(año) >
                                        new Date().getFullYear() && {
                                        color: "#1976d2",
                                        fontStyle: "italic",
                                      }),
                                      // Estilo para año actual
                                      ...(año ===
                                        String(new Date().getFullYear()) && {
                                        fontWeight: "bold",
                                      }),
                                    }}
                                  >
                                    <ListItemText
                                      primary={getEtiquetaAño(parseInt(año))}
                                      secondary={
                                        parseInt(año) > new Date().getFullYear()
                                          ? "Planificación futura"
                                          : null
                                      }
                                    />
                                  </ListItemButton>
                                ))}
                              </Box>
                            ))}
                        </List>
                      </Box>
                    </Popover>
                  </>
                )}

                {periodoVista === "temporada" && (
                  <>
                    <TextField
                      size="small"
                      value={temporadaSeleccionada}
                      onClick={handleClick}
                      InputProps={{
                        readOnly: true,
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarTodayIcon sx={{ color: "#1976d2" }} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        minWidth: 180,
                        "& .MuiOutlinedInput-root": {
                          borderRadius: "8px",
                          cursor: "pointer",
                          "&:hover": {
                            borderColor: "#1976d2",
                          },
                        },
                      }}
                    />
                    <Popover
                      open={Boolean(anchorEl)}
                      anchorEl={anchorEl}
                      onClose={handleClose}
                      anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "left",
                      }}
                      transformOrigin={{
                        vertical: "top",
                        horizontal: "left",
                      }}
                      PaperProps={{
                        sx: {
                          width: 300,
                          maxHeight: 400,
                        },
                      }}
                    >
                      <Box sx={{ p: 2 }}>
                        <TextField
                          fullWidth
                          size="small"
                          placeholder="Buscar temporada..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <SearchIcon />
                              </InputAdornment>
                            ),
                          }}
                          sx={{ mb: 2 }}
                        />
                        <List sx={{ maxHeight: 300, overflow: "auto" }}>
                          {Object.entries(temporadasPorDecada)
                            .sort(
                              ([decadaA], [decadaB]) =>
                                Number(decadaB) - Number(decadaA)
                            )
                            .map(([decada, temporadas]) => (
                              <Box key={decada}>
                                <ListItem sx={{ bgcolor: "#f5f5f5", py: 0.5 }}>
                                  <ListItemText
                                    primary={`Década ${decada}`}
                                    primaryTypographyProps={{
                                      fontWeight: "bold",
                                      fontSize: "0.9rem",
                                      color: "#666",
                                    }}
                                  />
                                </ListItem>
                                {temporadas.map((temporada) => (
                                  <ListItemButton
                                    key={temporada}
                                    onClick={() =>
                                      handleTemporadaSelect(temporada)
                                    }
                                    selected={
                                      temporada === temporadaSeleccionada
                                    }
                                    sx={{
                                      pl: 3,
                                      "&.Mui-selected": {
                                        bgcolor: "#e3f2fd",
                                        "&:hover": {
                                          bgcolor: "#bbdefb",
                                        },
                                      },
                                    }}
                                  >
                                    <ListItemText primary={temporada} />
                                  </ListItemButton>
                                ))}
                              </Box>
                            ))}
                        </List>
                      </Box>
                    </Popover>
                  </>
                )}
              </Box>
            </Box>
          </Box>
        </Paper>

        {/* Primera fila de gráficos */}
        <Grid container spacing={3}>
          {[
            {
              title: "Rendimiento por Lote (kg/ha)",
              type: "bar",
              data: obtenerDatosFiltrados.rendimientoPorLote,
              colors: ["#4CAF50", "#2196F3", "#FFC107", "#E91E63"],
              showValues: true,
            },
            {
              title: "Distribución de Cultivos (%)",
              type: "pie",
              data: obtenerDatosFiltrados.distribucionCultivos,
              colors: ["#2E7D32", "#1976D2", "#FFA000", "#D32F2F"],
              showValues: true,
            },
            {
              title: "Servicios Realizados por Mes",
              type: "line",
              data: obtenerDatosFiltrados.serviciosMensuales,
              colors: ["#2E7D32"],
              showValues: true,
            },
            {
              title: "Comparativa de Hectáreas Trabajadas (ha)",
              type: "bar",
              data: obtenerDatosFiltrados.hectareasTrabajadas,
              colors: ["#9C27B0", "#FF5722", "#795548", "#607D8B"],
              showValues: true,
            },
          ].map((chart, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Paper
                elevation={0}
                sx={{
                  borderRadius: "8px",
                  backgroundColor: "#ffffff",
                  overflow: "hidden",
                  "&:hover": {
                    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                  },
                  padding: 2,
                }}
              >
                <CustomChart
                  title={chart.title}
                  type={chart.type as "bar" | "line" | "pie"}
                  data={chart.data}
                  dataKey="value"
                  nameKey="name"
                  colors={chart.colors}
                  showValues={chart.showValues}
                />
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>
    </>
  );
};
export default Graficos;
