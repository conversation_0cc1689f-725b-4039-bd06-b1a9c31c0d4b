import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  TablePagination,
  Tooltip,
  Button,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import "../../globals.css";
import DeleteRoundedIcon from "@mui/icons-material/DeleteRounded";
import EditRoundedIcon from "@mui/icons-material/EditRounded";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";

// Definir estilos utilizando Styled Components
const CustomTableContainer = styled(TableContainer)`
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: none;
  padding: 16px;
`;

const ActionIconButton = styled(IconButton)`
  background-color: #f5fffa;
  margin-right: 8px;
  &:hover {
    background-color: #f5fffa;
    transform: scale(1.1);
  }
`;

const CustomTableHead = styled(TableHead)`
  && {
    background: #2e7d32;
    color: #ffffff;
    .MuiTableCell-root {
      padding-right: 24px;
      padding-left: 24px;
      font-weight: bold;
      text-transform: uppercase;
    }
  }
`;

const CustomHeaderCell = styled(TableCell)`
  && {
    font-family: "Roboto", sans-serif;
    font-weight: 600;
    color: #ffffff;
    letter-spacing: 0.5px;
  }
`;

const CustomTableRow = styled(TableRow)`
  transition: background-color 0.3s ease, transform 0.2s ease;
  &:hover {
    background-color: #e8f5e9 !important;
    transform: scale(1.01);
  }
`;

const CustomPageNumberContainer = styled("div")`
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  background-color: #4caf50;
  color: #fff;
  font-weight: bold;
  margin-right: 6px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #388e3c;
  }
`;

interface ShowActionsState {
  [key: number]: boolean;
}

export default function Datatable({
  columns,
  rows,
  option,
  optionDeleteFunction,
  optionUpdateFunction,
  optionSelect,
  setSelectedRow,
  selectedRow,
}: {
  columns: Array<{ field: string; headerName: string }>;
  rows: Array<Record<string, any>>;
  option: boolean;
  optionDeleteFunction: (id: number) => void;
  optionUpdateFunction: (id: number) => void;
  optionSelect: (id: number) => void;
  setSelectedRow: (row: Record<string, any> | null) => void;
  selectedRow: Record<string, any> | null;
}): JSX.Element {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = (
    _event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  const [showActions, setShowActions] = useState<ShowActionsState>({});
  const handleToggleActions = (rowId: number): void => {
    setShowActions((prev) => ({
      ...prev,
      [rowId as number]: !prev[rowId as number], // invierte el estado de esa fila
    }));
  };

  return (
    <CustomTableContainer>
      <Table>
        <CustomTableHead>
          <TableRow>
            {columns.map((column: { headerName: string }, index: number) => (
              <CustomHeaderCell key={index}>
                {column.headerName}
              </CustomHeaderCell>
            ))}
            {option && <CustomHeaderCell>Acciones</CustomHeaderCell>}
          </TableRow>
        </CustomTableHead>
        <TableBody>
          {rows
            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
            .map((row: Record<string, any>, index: number) => (
              <CustomTableRow
                key={index}
                onClick={() => {
                  setSelectedRow(row);
                }}
                className={selectedRow === row ? "is-selected" : ""}
              >
                {columns.map(
                  (
                    column: { field: string; headerName: string },
                    index: number
                  ) => (
                    <TableCell
                      key={index}
                      style={{
                        visibility:
                          column.field === "id" ? "hidden" : "visible",
                      }}
                    >
                      {row[column.field]}
                    </TableCell>
                  )
                )}

                {option && (
                  <TableCell>
                    {!showActions[row.id] ? (
                      <Button
                        variant="contained"
                        onClick={(e) => {
                          e.stopPropagation(); // Evita que se dispare el onClick del row
                          handleToggleActions(row.id);
                        }}
                        sx={{
                          backgroundColor: "#2E7D32", // Color verde más oscuro
                          "&:hover": {
                            backgroundColor: "#1B5E20", // Un verde aún más oscuro para el hover
                          },
                        }}
                      >
                        Ver
                      </Button>
                    ) : (
                      <>
                        <Tooltip
                          title="Eliminar"
                          arrow
                          componentsProps={{
                            tooltip: {
                              sx: {
                                backgroundColor: "#66BB6A",
                                color: "#fff",
                                fontSize: "1rem",
                                fontWeight: "bold",
                                padding: "8px 12px",
                                borderRadius: "4px",
                                "&::before": {
                                  backgroundColor: "#66BB6A",
                                },
                              },
                            },
                            arrow: {
                              sx: {
                                color: "#66BB6A",
                              },
                            },
                          }}
                        >
                          <ActionIconButton
                            onClick={(e) => {
                              e.stopPropagation();
                              optionDeleteFunction(row.id);
                            }}
                          >
                            <DeleteRoundedIcon sx={{ color: "#FF0000" }} />
                          </ActionIconButton>
                        </Tooltip>

                        <Tooltip
                          title="Editar"
                          arrow
                          componentsProps={{
                            tooltip: {
                              sx: {
                                backgroundColor: "#66BB6A",
                                color: "#fff",
                                fontSize: "1rem",
                                fontWeight: "bold",
                                padding: "8px 12px",
                                borderRadius: "4px",
                                "&::before": {
                                  backgroundColor: "#66BB6A",
                                },
                              },
                            },
                            arrow: {
                              sx: {
                                color: "#66BB6A",
                              },
                            },
                          }}
                        >
                          <ActionIconButton
                            onClick={(e) => {
                              e.stopPropagation();
                              optionUpdateFunction(row.id);
                            }}
                          >
                            <EditRoundedIcon sx={{ color: "#FFCC00" }} />
                          </ActionIconButton>
                        </Tooltip>

                        <Tooltip
                          title="Seleccionar"
                          arrow
                          componentsProps={{
                            tooltip: {
                              sx: {
                                backgroundColor: "#66BB6A",
                                color: "#fff",
                                fontSize: "1rem",
                                fontWeight: "bold",
                                padding: "8px 12px",
                                borderRadius: "4px",
                                "&::before": {
                                  backgroundColor: "#66BB6A",
                                },
                              },
                            },
                            arrow: {
                              sx: {
                                color: "#66BB6A",
                              },
                            },
                          }}
                        >
                          <ActionIconButton
                            onClick={(e) => {
                              e.stopPropagation();
                              optionSelect(row.id);
                            }}
                          >
                            <CheckCircleOutlineIcon sx={{ color: "#0000FF" }} />
                          </ActionIconButton>
                        </Tooltip>

                        {/** Botón para volver a ocultar acciones */}
                        <Button
                          variant="outlined"
                          color="success"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleToggleActions(row.id);
                          }}
                          style={{ marginLeft: 8 }}
                        >
                          Ocultar
                        </Button>
                      </>
                    )}
                  </TableCell>
                )}
              </CustomTableRow>
            ))}
        </TableBody>
      </Table>
      <TablePagination
        component="div"
        count={rows.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[5, 10, 25, 50, 100, { label: "Todos", value: -1 }]}
        nextIconButtonProps={{ style: { color: "#424242" } }}
        backIconButtonProps={{ style: { color: "#424242" } }}
        SelectProps={{
          MenuProps: {
            sx: {
              "& .MuiMenuItem-root": {
                color: "#424242",
                fontFamily: "var(--font-sans)",
              },
            },
          },
        }}
        style={{ color: "#424242" }}
        labelDisplayedRows={({ from, to, count }) =>
          `${from}-${to} de ${count}`
        }
      />
    </CustomTableContainer>
  );
}
