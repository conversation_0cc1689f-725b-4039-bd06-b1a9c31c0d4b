Stack trace:
Frame         Function      Args
0007FFFFA100  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9000) msys-2.0.dll+0x1FE8E
0007FFFFA100  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3D8) msys-2.0.dll+0x67F9
0007FFFFA100  000210046832 (000210286019, 0007FFFF9FB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA100  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA100  000210068E24 (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA3E0  00021006A225 (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF205E0000 ntdll.dll
7FFF1F130000 KERNEL32.DLL
7FFF1DA00000 KERNELBASE.dll
7FFF19A50000 apphelp.dll
7FFF1FB20000 USER32.dll
7FFF1E290000 win32u.dll
7FFF1E440000 GDI32.dll
000210040000 msys-2.0.dll
7FFF1E150000 gdi32full.dll
7FFF1DDF0000 msvcp_win.dll
7FFF1D730000 ucrtbase.dll
7FFF1FA60000 advapi32.dll
7FFF1EE30000 msvcrt.dll
7FFF1ED80000 sechost.dll
7FFF1F3B0000 RPCRT4.dll
7FFF1CD30000 CRYPTBASE.DLL
7FFF1DF30000 bcryptPrimitives.dll
7FFF1F920000 IMM32.DLL
