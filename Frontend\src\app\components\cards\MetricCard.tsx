import React from "react";
import { <PERSON>, Typo<PERSON>, Box, Button, Skeleton } from "@mui/material";
import IconoPersonalizado from "../icon/IconoPersonalizado";

interface MetricCardProps {
  title: string;
  value: string | number;
  change: string;
  icon: string;
  bgColor: string;
  hoverColor: string;
  loading?: boolean;
  onViewGraphClick?: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  loading = false,
  onViewGraphClick,
}) => {
  if (loading) {
    return (
      <Card
        sx={{
          backgroundColor: "#f8fafc",
          borderRadius: "16px",
          boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
          padding: "16px",
        }}
      >
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          mb={2}
        >
          <Skeleton variant="text" width="50%" height={24} />
          <Skeleton variant="circular" width={40} height={40} />
        </Box>
        <Skeleton variant="text" width="70%" height={48} />
        <Skeleton variant="text" width="40%" height={24} sx={{ mt: 1 }} />
        <Skeleton
          variant="rectangular"
          width="100%"
          height={36}
          sx={{ mt: 2, borderRadius: 1 }}
        />
      </Card>
    );
  }

  return (
    <Card
      sx={{
        padding: (theme) => theme.spacing(2),
        backgroundColor: "#ffffff",
        borderRadius: "8px",
        border: "1px solid #E5E7EB",
        boxShadow: "2px 2px 0px rgba(31, 142, 235, 0.2)",
        marginBottom: (theme) => theme.spacing(2),
        transition: "all 0.2s ease",
        "&:hover": {
          transform: "translate(-1px, -1px)",
          boxShadow: "3px 3px 0px rgba(31, 142, 235, 0.3)",
        },
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography
          variant="body1"
          fontWeight="bold"
          sx={{ color: "#000000", fontFamily: "Lexend, sans-serif" }}
        >
          {title}
        </Typography>
        <Box
          sx={{
            borderRadius: "50%",
            width: "40px",
            height: "40px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <IconoPersonalizado icono={icon} width={24} height={24} />
        </Box>
      </Box>
      <Typography
        variant="h4"
        fontWeight="bold"
        sx={{ color: "#000000", fontFamily: "Inter" }}
      >
        {value}
      </Typography>
      <Typography
        variant="body2"
        sx={{
          color: "#666666",
          fontWeight: "600",
          mt: 1,
        }}
      >
        {change}
      </Typography>
    </Card>
  );
};

export default MetricCard;
