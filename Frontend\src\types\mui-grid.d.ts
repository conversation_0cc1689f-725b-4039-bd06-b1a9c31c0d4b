// Type declarations to fix MUI component issues
import * as React from "react";

// Completely override MUI Grid to accept any props
declare module "@mui/material/Grid" {
  const Grid: React.ComponentType<any>;
  export default Grid;
}

// Override MUI ListItem to accept any props
declare module "@mui/material/ListItem" {
  const ListItem: React.ComponentType<any>;
  export default ListItem;
}

// Fix for mapbox types
declare module "mapbox-gl" {
  export interface MapboxEvent {
    lngLat: { lng: number; lat: number };
  }
}

// Fix for react-map-gl
declare module "react-map-gl" {
  export const Map: React.ComponentType<any>;
  export const NavigationControl: React.ComponentType<any>;
  export default Map;
}
