// import React from 'react';
// import { LineChart } from '@mui/x-charts';
// import CustomTooltip from "../../components/charts/CustomTooltip";

// const MyLineChart = () => {
//   const data = [
//     { label: 'Enero', series1: 2500, series2: 2000 },
//     { label: 'Febrero', series1: 2700, series2: 2200 },
//     { label: 'Marzo', series1: 2300, series2: 1800 },
//     { label: 'Abril', series1: 2900, series2: 2600 },
//     { label: 'Mayo', series1: 3100, series2: 2100 },
//   ];

//   return (
//     <LineChart
//       width={600}
//       height={400}
//       xAxis={[{ dataKey: 'label', label: 'Meses' }]}
//       yAxis={[{ label: 'Canti<PERSON> (USD)' }]}
//       series={[
//         {
//             dataKey: 'series1',
//             label: 'Series 1',
//             color: '#00BFA6',
//             showMark: true,       // Muestra los puntos en cada dato
//             curve: "monotoneX"    // Suaviza la línea
//         },
//         {
//             dataKey: 'series2',
//             label: 'Series 2',
//             color: '#1E88E5',
//             showMark: true,
//             curve: "monotoneX"
//         },
//       ]}
//       tooltip={{ tooltipComponent: CustomTooltip } as any}
//     />
//   );
// };

// export default MyLineChart;
