
spring.datasource.url=**********************************************
spring.datasource.username=postgres
spring.datasource.password=AlgoritmoSoftware
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.hibernate.ddl-auto=update
# This will create table automatically in your database
#/*update* *create* *create-drop*/

#puerto del servidor
server.port=8080

# 1- Ir a contraseña de aplicacion de la cuenta de Gmail
# 2-En seleccionar app => Eliegir <PERSON>(nombre personalizado)
# 3-Poner nombre personalizado y genear 
# 4-Usar contraseña generada que se meuestra en pantalla

#configuracion para enviar correos
spring.mail.host=smtp.gmail.com
spring.mail.port=465
#correo electronico para enviar las notificaciones
spring.mail.username=joa<PERSON><PERSON><PERSON><PERSON>@gmail.com
spring.mail.password=hmujstgnhtxpziun
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.ssl.enable= true

