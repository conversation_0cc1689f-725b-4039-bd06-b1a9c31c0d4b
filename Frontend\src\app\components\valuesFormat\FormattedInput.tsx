import React, { useState } from "react";
import TextField from "@mui/material/TextField";

interface FormattedInputProps {
  label?: string; // Etiqueta del TextField
  placeholder?: string; // Placeholder para guiar al usuario
  value?: string; // Valor inicial
  onChange?: (formattedValue: string) => void; // Callback para obtener el valor formateado
  width?: string | number; // Ancho personalizado en % o px
  size?: "small" | "medium"; // Add this line to support the size prop
  sx?: any; // Add this line to support the sx prop
  fullWidth?: boolean; // Add this line
}

const FormattedInput: React.FC<FormattedInputProps> = ({
  label = "", // Cambiado de "Monto" a cadena vacía
  placeholder = "Ejemplo: 1.236.365,12",
  value = "",
  onChange,
  width = "100%", // Valor predeterminado para el ancho
  size,
  sx,
}) => {
  const [internalValue, setInternalValue] = useState(value);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let inputValue = e.target.value;

    // Permitir solo números, comas, puntos y limitar a dos decimales
    inputValue = inputValue.replace(/[^0-9.,]/g, ""); // Eliminar caracteres no válidos
    const parts = inputValue.split(",");

    if (parts.length > 2) {
      inputValue = parts.slice(0, 2).join(","); // Evitar múltiples comas
    }

    if (parts[1] && parts[1].length > 2) {
      parts[1] = parts[1].slice(0, 2); // Limitar a 2 decimales
      inputValue = parts.join(",");
    }

    // Formatear miles correctamente
    const numberParts = inputValue.split(",");
    numberParts[0] = numberParts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ".");

    const formattedValue = numberParts.join(",");
    setInternalValue(formattedValue);

    if (onChange) {
      onChange(formattedValue); // Notificar el valor formateado al componente padre
    }
  };

  const defaultSx = {
    '& .MuiInputBase-root': {
      height: 'auto'  // Cambiado de '40px' a 'auto' para usar la altura por defecto
    },
    width
  };

  return (
    <TextField
      label={label}
      variant="outlined"
      value={internalValue}
      onChange={handleInputChange}
      placeholder={placeholder}
      size={size}
      sx={{ ...defaultSx, ...sx }}
    />
  );
};

export default FormattedInput;
