"use client";
// import React, { useState } from "react";
// import { useF<PERSON>, SubmitHandler, FieldValues } from "react-hook-form";
// import { Paper, Grid, Avatar,Typography,Button, TextField,Snackbar,Alert,InputAdornment,IconButton,} from "@mui/material";

// import PersonIcon from "@mui/icons-material/Person";
// import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
// import EmailIcon from "@mui/icons-material/Email";
// import Visibility from "@mui/icons-material/Visibility";
// import VisibilityOff from "@mui/icons-material/Visibility";
// import LockIcon from "@mui/icons-material/Lock";

// const Registar = () => {
//   const {  handleSubmit, formState: { errors },register,} = useForm();
//   // Estados para manejo de carga y notificaciones
//   const [isLoading, setIsLoading] = useState(false);
//   const [successMessage, setSuccessMessage] = useState("");
//   const [openSnackbar, setOpenSnackbar] = useState(false);
//   const [showPassword, setShowPassword] = useState(false);

//   const onSubmit = async (data: { nombre: string; apellido: string; mail: string; password: string }) => {
//     const nuevoUsuario = {
//       nombre: data.nombre,
//       apellido: data.apellido,
//       mail: data.mail,
//       password: data.password,
//     };

//     setIsLoading(true);
//     try {
//       const res = await fetch("http://localhost:8080/api/usuarios", {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(nuevoUsuario),
//       });

//       setSuccessMessage("Registro exitoso. ¡Bienvenido!");
//       setOpenSnackbar(true);
//     } catch (error) {
//       console.error("Error en la solicitud:", error);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Cerrar el Snackbar
//   const handleCloseSnackbar = () => {
//     setOpenSnackbar(false);
//   };

//   const handleClickShowPassword = () => {
//     setShowPassword((prev) => !prev);
//   };

//   return (
//     <div
//       style={{
//         padding: 20,
//         height: "auto",
//         width: 500,
//         margin: "25px auto",
//         border: "1px solid #ccc",
//         borderRadius: "8px",
//         display: "flex",
//         justifyContent: "center",
//         alignItems: "center",
//       }}
//     >
//       <Paper
//         elevation={4}
//         sx={{
//           p: 4,
//           borderRadius: "8px",
//           maxWidth: 400,
//           width: "100%",
//           boxShadow: "0px 10px 20px rgba(0,0,0,0.1)",
//         }}
//       >
//         {/* <Avatar
//           sx={{
//             width: 60,
//             height: 60,
//             backgroundColor: "#FFFFFF",
//             margin: "0 auto 20px",
//             border: "2px solid #1976d2",
//           }}
//         >
//           <IconoPersonalizado icono="registrar.png" width={32} height={32} />
//         </Avatar> */}

//         <Typography
//           variant="h5"
//           component="h1"
//           sx={{
//             textAlign: "center",
//             fontWeight: "bold",
//             mb: 2,
//           }}
//         >
//           Registrarse
//         </Typography>

//         <Typography
//           variant="body2"
//           component="p"
//           sx={{
//             textAlign: "center",
//             color: "text.secondary",
//             mb: 3,
//           }}
//         >
//           ¡Por favor complete este formulario para crear una cuenta!
//         </Typography>

//         <form onSubmit={handleSubmit(onSubmit as SubmitHandler<FieldValues>)}>
//           <Grid container spacing={2}>
//             {/* Campo de Nombre */}
//             <Grid item xs={12} sm={6}>
//               <TextField
//                 label="Nombre"
//                 variant="outlined"
//                 fullWidth
//                 {...register("nombre", {
//                   required: "Este campo es obligatorio",
//                 })}
//                 error={!!errors.nombre}
//                 helperText={errors.nombre ? errors.nombre.message : ""}
//                 sx={{ mb: 2 }}
//                 InputProps={{
//                   startAdornment: (
//                     <InputAdornment position="start">
//                       <PersonIcon color={errors.nombre ? "error" : "action"} />
//                     </InputAdornment>
//                   ),
//                 }}
//               />
//             </Grid>

//             {/* Campo de Apellido */}
//             <Grid item xs={12} sm={6}>
//               <TextField
//                 label="Apellido"
//                 variant="outlined"
//                 fullWidth
//                 {...register("apellido", {
//                   required: "Este campo es obligatorio",
//                 })}
//                 error={!!errors.apellido}
//                 helperText={errors.apellido ? errors.apellido.message : ""}
//                 sx={{ mb: 2 }}
//                 InputProps={{
//                   startAdornment: (
//                     <InputAdornment position="start">
//                       <PersonOutlineIcon
//                         color={errors.apellido ? "error" : "action"}
//                       />
//                     </InputAdornment>
//                   ),
//                 }}
//               />
//             </Grid>

//             {/* Campo de Correo electrónico */}
//             <Grid item xs={12}>
//               <TextField
//                 label="Correo electrónico"
//                 variant="outlined"
//                 fullWidth
//                 {...register("mail", {
//                   required: "Este campo es obligatorio",
//                   pattern: {
//                     value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
//                     message: "Correo electrónico inválido",
//                   },
//                 })}
//                 error={!!errors.mail}
//                 helperText={errors.mail ? errors.mail.message : ""}
//                 sx={{ mb: 2 }}
//                 InputProps={{
//                   startAdornment: (
//                     <InputAdornment position="start">
//                       <EmailIcon color={errors.mail ? "error" : "action"} />
//                     </InputAdornment>
//                   ),
//                 }}
//               />
//             </Grid>

//             {/* Campo de Contraseña */}
//             <Grid item xs={12}>

//               <TextField
//                 label="Contraseña"
//                 variant="outlined"
//                 type={showPassword ? "text" : "password"}
//                 fullWidth
//                 {...register("password", {
//                   required: "Este campo es obligatorio",
//                   minLength: {
//                     value: 6,
//                     message: "La contraseña debe tener al menos 6 caracteres",
//                   },
//                 })}
//                 error={!!errors.password}
//                 helperText={errors.password ? errors.password.message : ""}
//                 sx={{ mb: 2 }}
//                 InputProps={{
//                   startAdornment: (
//                     <InputAdornment position="start">
//                       <LockIcon color={errors.password ? "error" : "action"} />
//                     </InputAdornment>
//                   ),
//                   endAdornment: (
//                     <InputAdornment position="end">
//                       <IconButton onClick={handleClickShowPassword} edge="end">
//                         {showPassword ? <VisibilityOff /> : <Visibility />}
//                       </IconButton>
//                     </InputAdornment>
//                   ),
//                 }}
//               />
//             </Grid>
//           </Grid>

//           <Button
//             type="submit"
//             variant="contained"
//             color="primary"
//             fullWidth
//             disabled={isLoading}
//             sx={{
//               textTransform: "none",
//               backgroundColor: "#3f51b5",
//               "&:hover": { backgroundColor: "#303f9f" },
//               "&:disabled": { backgroundColor: "#9e9e9e" },
//             }}
//           >
//             {isLoading ? "Registrando..." : "Registrarse"}
//           </Button>
//         </form>
//       </Paper>

//       <Snackbar
//         open={openSnackbar}
//         autoHideDuration={6000}
//         onClose={handleCloseSnackbar}
//         anchorOrigin={{ vertical: "top", horizontal: "center" }}
//       >
//         <Alert
//           onClose={handleCloseSnackbar}
//           severity="success"
//           sx={{ width: "100%" }}
//         >
//           {successMessage}
//         </Alert>
//       </Snackbar>
//     </div>
//   );
// };

// export default Registar;

"use client";
import React, { useState } from "react";
import { useForm, SubmitHandler, FieldValues } from "react-hook-form";
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Link,
  InputAdornment,
} from "@mui/material";
import Image from "next/image";
// Importar los iconos necesarios
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import EmailOutlinedIcon from "@mui/icons-material/EmailOutlined";
import LockOutlinedIcon from "@mui/icons-material/LockOutlined";

const Registrar = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const onSubmit = async (data: {
    nombreCompleto: string;
    mail: string;
    password: string;
    confirmPassword: string;
  }) => {
    console.log(data);
  };

  const inputStyles = {
    "& .MuiOutlinedInput-root": {
      backgroundColor: "#ffffff",
      borderRadius: "8px",
      "& fieldset": {
        borderColor: "#e0e0e0",
      },
      "&:hover fieldset": {
        borderColor: "#1976d2",
      },
      "&.Mui-focused fieldset": {
        borderColor: "#1976d2",
      },
    },
    "& .MuiInputAdornment-root": {
      color: "#666666",
    },
  };

  const labelStyles = {
    fontWeight: 600,
    color: "#333",
    marginBottom: "8px",
    display: "block",
  };

  return (
    <Box sx={{ minHeight: "100vh", width: "100%", position: "relative" }}>
      {/* Background Image with Overlay */}
      <Box
        sx={{
          position: "fixed",
          inset: 0,
          width: "100%",
          height: "100%",
          zIndex: 0,
          "&::before": {
            content: '""',
            position: "absolute",
            inset: 0,
            background:
              "linear-gradient(to right, rgba(30,64,175,0.4), rgba(22,101,52,0.4))",
            mixBlendMode: "multiply",
            zIndex: 1,
          },
        }}
      >
        <Image
          src="/assets/img/fondo.jpg"
          alt="Farm landscape"
          fill
          sizes="100vw"
          style={{ objectFit: "cover" }}
          quality={100}
          priority
        />
      </Box>

      {/* Content */}
      <Box
        sx={{
          position: "relative",
          zIndex: 1,
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          py: 4,
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 4,
            width: "100%",
            maxWidth: "400px",
            bgcolor: "white",
            borderRadius: 2,
          }}
        >
          <Typography
            variant="h5"
            component="h1"
            sx={{
              textAlign: "center",
              mb: 4,
              fontWeight: 600,
              color: "#1a1a1a",
            }}
          >
            Crear una cuenta
          </Typography>

          <form onSubmit={handleSubmit(onSubmit as SubmitHandler<FieldValues>)}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={labelStyles}>
                Nombre Completo
              </Typography>
              <TextField
                fullWidth
                placeholder="Juan Pérez"
                variant="outlined"
                sx={inputStyles}
                {...register("nombreCompleto", {
                  required: "Este campo es requerido",
                })}
                error={!!errors.nombreCompleto}
                helperText={errors.nombreCompleto?.message?.toString()}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PersonOutlineIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={labelStyles}>
                Correo Electrónico
              </Typography>
              <TextField
                fullWidth
                placeholder="<EMAIL>"
                variant="outlined"
                sx={inputStyles}
                {...register("mail", {
                  required: "Este campo es requerido",
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: "Correo electrónico inválido",
                  },
                })}
                error={!!errors.mail}
                helperText={errors.mail?.message?.toString()}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailOutlinedIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={labelStyles}>
                Contraseña
              </Typography>
              <TextField
                fullWidth
                type="password"
                placeholder="********"
                variant="outlined"
                sx={inputStyles}
                {...register("password", {
                  required: "Este campo es requerido",
                })}
                error={!!errors.password}
                helperText={errors.password?.message?.toString()}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockOutlinedIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" sx={labelStyles}>
                Confirmar Contraseña
              </Typography>
              <TextField
                fullWidth
                type="password"
                placeholder="********"
                variant="outlined"
                sx={inputStyles}
                {...register("confirmPassword", {
                  required: "Este campo es requerido",
                })}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword?.message?.toString()}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockOutlinedIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{
                py: 1.5,
                textTransform: "none",
                fontSize: "1rem",
                backgroundColor: "#1976d2",
                "&:hover": {
                  backgroundColor: "#1565c0",
                },
              }}
            >
              Registrarse
            </Button>

            <Box sx={{ mt: 2, textAlign: "center" }}>
              <Typography variant="body2" color="text.secondary">
                ¿Ya tienes una cuenta?{" "}
                <Link
                  href="/auth/login"
                  sx={{ color: "#1976d2", textDecoration: "none" }}
                >
                  Inicia sesión aquí
                </Link>
              </Typography>
            </Box>
          </form>
        </Paper>
      </Box>
    </Box>
  );
};

export default Registrar;
