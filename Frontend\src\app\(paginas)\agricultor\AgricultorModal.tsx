import React, { useState, useMemo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>rid,

  Ta<PERSON>,
  Tab,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogContentText,
  FormControl,
  FormHelperText,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Typography,
  Box,
} from "@mui/material";
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Person as PersonIcon,
  AddCircle as AddCircleIcon,
} from "@mui/icons-material";

interface Client {
  id: number;
  propietarioNombre?: string;
  propietarioDocumento?: string;
  propietarioTelefono?: string;
  propietarioEmail?: string;
  razonSocial: string;
  tipoCliente?: string;
  direccion: string;
  empresaLocalidad?: string;
  provincia?: string;
  condFrenteIva: string;
  nombreContacto?: string;
  cargoContacto?: string;
  telefono: string;
  mail: string;
  lugar: string;
  documento: string;
}

interface AgricultorModalProps {
  open: boolean;
  onClose: (event?: React.MouseEvent<HTMLElement>, reason?: string) => void;
  onSubmit: (formData: any) => void;
  estadoModal: "add" | "update";
  initialData?: Client | null;
}

const AgricultorModal: React.FC<AgricultorModalProps> = ({
  open,
  onClose,
  onSubmit,
  estadoModal,
  initialData,
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [enabledTabs, setEnabledTabs] = useState<boolean[]>([true, false, false]);
  const [validationErrors, setValidationErrors] = useState<{
    [key: string]: string;
  }>({});

  const [formData, setFormData] = useState({
    propietarioNombre: initialData?.propietarioNombre || "",
    propietarioDocumento: initialData?.propietarioDocumento || "",
    propietarioTelefono: initialData?.propietarioTelefono || "",
    propietarioEmail: initialData?.propietarioEmail || "",
    usarComoRazonSocial: false,
    empresaRazonSocial: initialData?.razonSocial || "",
    empresaTipoCliente: initialData?.tipoCliente || "",
    empresaDomicilio: initialData?.direccion || "",
    empresaLocalidad: initialData?.empresaLocalidad || "",
    empresaProvincia: initialData?.provincia || "",
    empresaCondFrenteIva: initialData?.condFrenteIva || "",
    contactos: [
      {
        id: 1,
        nombre: initialData?.nombreContacto || "",
        cargo: initialData?.cargoContacto || "",
        telefono: initialData?.telefono || "",
        email: initialData?.mail || "",
      },
    ],
  });

  const provincias = useMemo(() => [
    "Buenos Aires", "Catamarca", "Chaco", "Chubut", "Córdoba", "Corrientes",
    "Entre Ríos", "Formosa", "Jujuy", "La Pampa", "La Rioja", "Mendoza",
    "Misiones", "Neuquén", "Río Negro", "Salta", "San Juan", "San Luis",
    "Santa Cruz", "Santa Fe", "Santiago del Estero", "Tierra del Fuego", "Tucumán",
  ], []);

  const tipoClienteOptions = useMemo(() => [
    { value: "Productor(comercial)", category: "Productores" },
    { value: "Productor(familiar)", category: "Productores" },
    { value: "Estancia", category: "Productores" },
    { value: "Empresa (persona jurídica, p. ej. SA / SRL)", category: "Empresas" },
    { value: "Cooperativa", category: "Empresas" },
    { value: "Asociación/Consorcio/Entidad Gremial", category: "Empresas" },
    { value: "Contratista(p. ej. otro que contrata equipo)", category: "Servicios" },
    { value: "Acopio/Industria/Exportador(silos, plantas, compradoras)", category: "Servicios" },
    { value: "Municipalidad/Estatal/Gubernamental", category: "Público" },
    { value: "Particular(pequeños clientes domésticos)", category: "Otros" },
    { value: "Otro(para casos no previstos)", category: "Otros" },
    { value: "No Especificado", category: "Otros" },
  ], []);

  const condFrenteIvaOptions = useMemo(() => [
    "IVA Responsable Inscripto", "IVA Responsable no Inscripto", "IVA no Responsable",
    "IVA Sujeto Exento", "Consumidor Final", "Responsable Monotributo",
    "Sujeto no Categorizado", "Proveedor del Exterior", "Cliente del Exterior",
    "IVA Liberado", "Pequeño Contribuyente Social", "Monotributista Social",
    "Pequeño Contribuyente Eventual",
  ], []);

  const labelStyles = useMemo(() => ({
    fontWeight: 600,
    color: "#333",
    marginBottom: "8px",
    display: "block",
    fontFamily: "Lexend, sans-serif",
  }), []);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    
    // Validaciones básicas
    if (name === "propietarioNombre" || name === "empresaRazonSocial" || name === "empresaLocalidad") {
      if (!/^[a-zA-ZÀ-ÿ\s]*$/.test(value)) {
        setValidationErrors((prev) => ({
          ...prev,
          [name]: "Solo se permiten letras y espacios",
        }));
        return;
      } else {
        setValidationErrors((prev) => ({
          ...prev,
          [name]: "",
        }));
      }
    }

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleNextTab = () => {
    if (tabValue < 2) {
      const newEnabledTabs = [...enabledTabs];
      newEnabledTabs[tabValue + 1] = true;
      setEnabledTabs(newEnabledTabs);
      setTabValue(tabValue + 1);
    }
  };

  const handlePreviousTab = () => {
    if (tabValue > 0) {
      setTabValue(tabValue - 1);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          width: "1100px",
          maxWidth: "95vw",
          minHeight: "600px",
        },
      }}
    >
      <Box sx={{ p: 4 }}>
        <Box sx={{ mb: 2 }}>
          <DialogTitle
            sx={{
              p: 0,
              fontFamily: "Lexend, sans-serif",
              fontSize: "1.5rem",
              fontWeight: "bold",
              color: "#333",
            }}
          >
            {estadoModal === "add" ? "Registrar nuevo agricultor/ganadero" : "Editar agricultor/ganadero"}
          </DialogTitle>
          <DialogContentText
            sx={{
              p: 0,
              mt: 1,
              fontFamily: "Inter, sans-serif",
              color: "#666",
            }}
          >
            Complete la información del agricultor/ganadero.
          </DialogContentText>
        </Box>
        <IconButton
          aria-label="close"
          onClick={(event) => onClose(event, "closeButtonClick")}
          sx={{ position: "absolute", right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
        <Box component="form" onSubmit={handleSubmit}>
          <DialogContent sx={{ p: 0 }}>
            <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 3 }}>
              <Tabs
                value={tabValue}
                onChange={(_, newValue) => {
                  if (enabledTabs[newValue]) {
                    setTabValue(newValue);
                  }
                }}
              >
                <Tab label="Propietario/Persona Física" disabled={!enabledTabs[0]} />
                <Tab label="Empresa/Razón Social" disabled={!enabledTabs[1]} />
                <Tab label="Contacto/Encargado" disabled={!enabledTabs[2]} />
              </Tabs>
            </Box>

            {/* Tab content would go here - simplified for performance */}
            {tabValue === 0 && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" sx={{ mb: 3, fontFamily: "Lexend, sans-serif", color: "#333" }}>
                  Datos del Propietario/Persona Física
                </Typography>
                <Grid container spacing={3}>
                  <Grid size={{ xs: 12 ,sm:6}}>
                    <Typography variant="body2" sx={labelStyles}>
                      Nombre Completo *
                    </Typography>
                    <TextField
                      placeholder="Ej: Juan Carlos Pérez"
                      name="propietarioNombre"
                      fullWidth
                      value={formData.propietarioNombre}
                      onChange={handleInputChange}
                      error={Boolean(validationErrors.propietarioNombre)}
                      helperText={validationErrors.propietarioNombre}
                    />
                  </Grid>
                  {/* More fields would go here */}
                </Grid>
                <Box sx={{ display: "flex", justifyContent: "space-between", mt: 3, pt: 2, borderTop: "1px solid #e0e0e0" }}>
                  <Button variant="outlined" onClick={(event) => onClose(event, "closeButtonClick")}>
                    Cancelar
                  </Button>
                  <Button variant="contained" onClick={handleNextTab} sx={{ bgcolor: "#2E7D32", "&:hover": { bgcolor: "#1B5E20" } }}>
                    Siguiente
                  </Button>
                </Box>
              </Box>
            )}

            {/* Other tabs would be implemented similarly */}
          </DialogContent>
        </Box>
      </Box>
    </Dialog>
  );
};

export default AgricultorModal;
