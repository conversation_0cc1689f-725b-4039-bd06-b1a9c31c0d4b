import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Lexend } from "next/font/google";
import * as React from "react";
import "./globals.css";
import { ThemeProviderWrapper } from "./components/ThemeProviderWrapper";

const inter = Inter({ subsets: ["latin"] });
const lexend = Lexend({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "AgroServicios",
  description: "Gestión de Servicios Agropecuarios",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es">
      <body className={inter.className}>
        <ThemeProviderWrapper>{children}</ThemeProviderWrapper>
      </body>
    </html>
  );
}
