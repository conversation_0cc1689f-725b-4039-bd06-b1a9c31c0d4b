import { NextRouter } from "next/router";

export const verificarLogin = async (router: NextRouter) => {
  if (typeof window !== "undefined") {
    try {
      // Agrega un console.log para verificar el contenido de localStorage
      console.log(
        "Contenido de localStorage en verificarLogin antes de la verificación:",
        localStorage.getItem("Login")
      );

      const loginData = localStorage.getItem("Login");
      const login = loginData ? JSON.parse(loginData) : null;

      if (login && login.loginCorrecto === true) {
        router.push("/dashboard");
      } else {
        router.push("/auth/container");
      }
    } catch (error) {
      console.error("Error al verificar login:", error);
      return false;
    }
  }

  return false;
};
