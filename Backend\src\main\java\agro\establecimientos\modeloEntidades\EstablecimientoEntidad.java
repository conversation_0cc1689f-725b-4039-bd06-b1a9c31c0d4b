
package agro.establecimientos.modeloEntidades;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import agro.lotes.modeloEntidades.LoteEntidad;
import agro.personas.modeloEntidades.PersonaEntidad;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "establecimientos")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EstablecimientoEntidad {

    @Id
    @GeneratedValue
    private UUID id;

    private String nombre;

    @ManyToOne
    @JoinColumn(name = "persona_id")
    @JsonBackReference
    private PersonaEntidad persona;
    
    private String lugar;
    
    @OneToMany(mappedBy = "establecimiento", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<LoteEntidad> lotes = new ArrayList<>();
}
