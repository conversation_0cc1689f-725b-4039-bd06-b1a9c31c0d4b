package agro.personas;

import java.util.List;
import java.util.UUID;
import org.springframework.stereotype.Service;

import agro.personas.modeloEntidades.PersonaEntidad;
import agro.personas.repositorioJPA.PersonaRepositorio;
import lombok.RequiredArgsConstructor;

/*Persona contienen la lógica de negocio de la aplicación.Logica de negocio @Persona*/
@RequiredArgsConstructor
@Service

public class PersonaCliente {

    private final PersonaRepositorio personaRepositorio;

    /* Guardar Persona */
    public void guardarPersona(PersonaEntidad personaNueva) {
        if (personaNueva == null) {
            throw new IllegalArgumentException("La persona proporcionada es nula.");
        }

        String documentoString = personaNueva.getDocumento();
        if (personaRepositorio.existsByDocumento(documentoString)) {
            throw new IllegalArgumentException("Ya existe una persona con el mismo documento.");
        }

        personaRepositorio.save(personaNueva);
    }

    /* Lista Persona */
    public List<PersonaEntidad> listaPersona() {

        return personaRepositorio.findAll();
    }

    /* Eliminar Persona */
    public void eliminarPersonaPorId(String idPersona) {
        personaRepositorio.deleteById(UUID.fromString(idPersona));
    }

    /* Obtener Persona */
    public PersonaEntidad obtenerPersonaPorId(String idPersona) {
        return personaRepositorio.findById(UUID.fromString(idPersona))
                .orElseThrow(() -> new RuntimeException("Persona no encontrada"));

    }

    /* Modificar Persona */
    public void modificarPersonaPorId(PersonaEntidad personaActualizada) {

        PersonaEntidad personaExistente = personaRepositorio.findById(personaActualizada.getId())
                .orElseThrow(() -> new RuntimeException("Persona no encontrada"));

        // Datos del Propietario/Persona Física (Pestaña 1)
        personaExistente.setPropietarioNombre(personaActualizada.getPropietarioNombre());
        personaExistente.setPropietarioDocumento(personaActualizada.getPropietarioDocumento());
        personaExistente.setPropietarioTelefono(personaActualizada.getPropietarioTelefono());
        personaExistente.setPropietarioEmail(personaActualizada.getPropietarioEmail());

        // Datos de la Empresa/Razón Social (Pestaña 2)
        personaExistente.setRazonSocial(personaActualizada.getRazonSocial());
        personaExistente.setTipoCliente(personaActualizada.getTipoCliente());
        personaExistente.setDireccion(personaActualizada.getDireccion());
        personaExistente.setEmpresaLocalidad(personaActualizada.getEmpresaLocalidad());
        personaExistente.setProvincia(personaActualizada.getProvincia());
        personaExistente.setCondFrenteIva(personaActualizada.getCondFrenteIva());

        // Datos del Contacto/Encargado Principal (Pestaña 3)
        personaExistente.setNombreContacto(personaActualizada.getNombreContacto());
        personaExistente.setCargoContacto(personaActualizada.getCargoContacto());
        personaExistente.setTelefono(personaActualizada.getTelefono());
        personaExistente.setMail(personaActualizada.getMail());

        // Campos calculados/derivados
        personaExistente.setLugar(personaActualizada.getLugar());
        personaExistente.setDocumento(personaActualizada.getDocumento());

        personaRepositorio.save(personaExistente);

    }

}
