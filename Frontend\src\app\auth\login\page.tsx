"use client";
// import React, { useState } from "react";
// import { Button,  Grid,Link, Paper,Typography, CircularProgress, TextField,InputAdornment, IconButton,} from "@mui/material";
// import { useRouter } from "next/navigation";
// import { useForm } from "react-hook-form";
// import EmailIcon from "@mui/icons-material/Email";
// import Visibility from "@mui/icons-material/Visibility";
// import VisibilityOff from "@mui/icons-material/VisibilityOff";

// const IniciarSesion = ({ activarInscribirse, activarRecuperarPassword }: { activarInscribirse: () => void; activarRecuperarPassword: () => void }) => {
//   const { handleSubmit,formState: { errors },} = useForm();
//   const router = useRouter();
//   const [userMail, setUserMail] = useState("");
//   const [userPassword, setUserContraseña] = useState("");
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState(false);
//   const [showPassword, setShowPassword] = useState(false);

//   const handleClickShowPassword = () => {
//     setShowPassword((prev) => !prev);
//   };

//   const clickHandler = async () => {
//     setLoading(true);
//     const verificarLogin = { mail: userMail, password: userPassword };
//     try {
//       const res = await fetch(
//         "http://localhost:8080/api/usuarios/verificar-login",
//         {
//           method: "POST",
//           headers: { "Content-Type": "application/json" },
//           body: JSON.stringify(verificarLogin),
//         }
//       );

//       const data = await res.json();
//       if (res.ok) {
//         localStorage.setItem("Login", JSON.stringify(data));
//         if (typeof data === "boolean" && data) {
//           router.push("/dashboard");
//         } else {
//           router.push("/auth/container");
//         }
//       } else {
//         router.push("/auth/container");
//       }
//     } catch (error) {
//       console.error("Error en la solicitud:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div
//       style={{
//         padding: 25,
//         height: "auto",
//         width: 500,
//         margin: "25px auto",
//         border: "1px solid #ccc",
//         borderRadius: "8px",
//         display: "flex",
//         justifyContent: "center",
//         alignItems: "center",
//       }}
//     >
//       <Paper
//         elevation={4}
//         sx={{
//           p: 4,
//           borderRadius: "8px",
//           maxWidth: 400,
//           width: "100%",
//           boxShadow: "0px 10px 20px rgba(0,0,0,0.1)",
//         }}
//       >
//         <Typography
//           variant="h5"
//           component="h1"
//           sx={{
//             textAlign: "center",
//             fontWeight: "bold",
//             mb: 2,
//           }}
//         >
//           ¡Bienvenido al Sistema!
//         </Typography>

//         <form onSubmit={handleSubmit(clickHandler)}>
//           <Grid container spacing={2}>
//             <Grid item xs={12}>
//               <TextField
//                 label="Correo electrónico"
//                 variant="outlined"
//                 fullWidth
//                 value={userMail}
//                 onChange={(e) => setUserMail(e.target.value)}
//                 error={!!errors.mail}
//                 helperText={errors.mail ? errors.mail.message : ""}
//                 sx={{ mb: 2 }}
//                 InputProps={{
//                   startAdornment: (
//                     <EmailIcon
//                       color={error ? "error" : "action"}
//                       sx={{ mr: 1 }}
//                     />
//                   ),
//                 }}
//               />
//             </Grid>

//             <Grid item xs={12}>
//               <TextField
//                 label="Contraseña"
//                 variant="outlined"
//                 type={showPassword ? "text" : "password"}
//                 fullWidth
//                 value={userPassword}
//                 onChange={(e) => setUserContraseña(e.target.value)}
//                 sx={{ mb: 2 }}
//                 InputProps={{
//                   endAdornment: (
//                     <InputAdornment position="end">
//                       <IconButton
//                         onClick={handleClickShowPassword}
//                         edge="end"
//                         aria-label="toggle password visibility"
//                       >
//                         {showPassword ? <VisibilityOff /> : <Visibility />}
//                       </IconButton>
//                     </InputAdornment>
//                   ),
//                 }}
//               />
//             </Grid>

//             <Grid item xs={12}>
//               <Button
//                 type="submit"
//                 fullWidth
//                 variant="contained"
//                 color="primary"
//                 disabled={loading}
//                 sx={{
//                   textTransform: "none",
//                   backgroundColor: "#3f51b5",
//                   "&:hover": { backgroundColor: "#303f9f" },
//                   "&:disabled": { backgroundColor: "#9e9e9e" },
//                 }}
//               >
//                 {loading ? (
//                   <CircularProgress size={24} color="secondary" />
//                 ) : (
//                   "Iniciar Sesión"
//                 )}
//               </Button>
//             </Grid>
//           </Grid>
//         </form>

//         <Grid container spacing={2} justifyContent="center" mt={3}>
//           {/* Contenedor para los dos textos en la misma fila */}
//           <Grid item xs={6} sx={{ display: "flex", justifyContent: "center" }}>
//             <Link
//               href="/auth/container/recuperar"
//               onClick={(e) => {
//                 e.preventDefault();
//                 activarRecuperarPassword();
//               }}
//             >
//               <Typography variant="body2">¿Olvidó la Contraseña?</Typography>
//             </Link>
//           </Grid>

//           <Grid item xs={6} sx={{ display: "flex", justifyContent: "center" }}>
//             <Typography variant="body2">¿No tienes una cuenta?</Typography>
//           </Grid>
//         </Grid>

//         {/* Botón Inscribirse debajo de los textos */}
//         <Grid container spacing={2} justifyContent="center" mt={1}>
//           <Grid item xs={12}>
//             <Button
//               fullWidth
//               variant="contained"
//               color="secondary"
//               onClick={activarInscribirse}
//               sx={{
//                 textTransform: "none",
//                 backgroundColor: "#9c27b0",
//                 "&:hover": { backgroundColor: "#7b1fa2" },
//               }}
//             >
//               Inscribirse
//             </Button>
//           </Grid>
//         </Grid>
//       </Paper>
//     </div>
//   );
// };

// export default IniciarSesion;

"use client";
"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Container,
  Alert,
  CircularProgress,
} from "@mui/material";
import EmailIcon from "@mui/icons-material/Email";
import LockIcon from "@mui/icons-material/Lock";
import Image from "next/image";

const LoginForm = () => {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState<{ email?: string; password?: string }>(
    {}
  );
  const [isLoading, setIsLoading] = useState(false);
  const [showError, setShowError] = useState(false);

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};

    if (!email) {
      newErrors.email = "El correo electrónico es obligatorio";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Correo electrónico inválido";
    }

    if (!password) {
      newErrors.password = "La contraseña es obligatoria";
    } else if (password.length < 6) {
      newErrors.password = "La contraseña debe tener al menos 6 caracteres";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);

    try {
      const res = await fetch(
        "http://localhost:8080/api/usuarios/verificar-login",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ mail: email, password }),
        }
      );

      const data = await res.json();

      if (res.ok) {
        localStorage.setItem("Login", JSON.stringify(data));
        router.push("/dashboard");
      } else {
        setShowError(true);
        router.push("/auth/container");
      }
    } catch (error) {
      console.error("Error en la solicitud:", error);
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box sx={{ minHeight: "100vh", width: "100%", position: "relative" }}>
      {/* Background Image */}
      <Box
        sx={{
          position: "fixed",
          inset: 0,
          width: "100%",
          height: "100%",
          zIndex: 0,
          "&::before": {
            content: '""',
            position: "absolute",
            inset: 0,
            background:
              "linear-gradient(to right, rgba(30,64,175,0.4), rgba(22,101,52,0.4))",
            mixBlendMode: "multiply",
            zIndex: 1,
          },
        }}
      >
        <Image
          src="/assets/img/fondo.jpg"
          alt="Farm landscape"
          fill
          sizes="100vw"
          style={{ objectFit: "cover" }}
          quality={100}
          priority
        />
      </Box>

      {/* Content */}
      <Box
        sx={{
          position: "relative",
          zIndex: 1,
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          py: 4,
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 4,
            width: "100%",
            maxWidth: "400px",
            bgcolor: "white",
            borderRadius: 2,
          }}
        >
          <Typography
            variant="h5"
            component="h1"
            sx={{
              mb: 4,
              textAlign: "center",
              fontSize: "24px",
              fontWeight: "600",
              fontFamily:
                "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            }}
          >
            Iniciar Sesión
          </Typography>

          <form onSubmit={handleSubmit}>
            <Typography
              variant="body2"
              sx={{
                mb: 1,
                fontSize: "14px",
                fontWeight: "500",
                fontFamily:
                  "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
              }}
            >
              Correo Electrónico
            </Typography>
            <TextField
              fullWidth
              placeholder="<EMAIL>"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              error={!!errors.email}
              helperText={errors.email}
              InputProps={{
                startAdornment: (
                  <EmailIcon sx={{ mr: 1, color: "action.active" }} />
                ),
                sx: {
                  fontFamily:
                    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                  fontSize: "14px",
                },
              }}
              variant="outlined"
              sx={{ mb: 3 }}
            />

            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 1,
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  fontSize: "14px",
                  fontWeight: "500",
                  fontFamily:
                    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                }}
              >
                Contraseña
              </Typography>
              <Link href="/auth/recuperar" style={{ textDecoration: "none" }}>
                <Typography
                  variant="body2"
                  color="primary"
                  sx={{
                    fontSize: "14px",
                    fontWeight: "400",
                    fontFamily:
                      "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                    color: "#2563EB",
                  }}
                >
                  ¿Olvidaste tu contraseña?
                </Typography>
              </Link>
            </Box>

            <TextField
              fullWidth
              placeholder="********"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              error={!!errors.password}
              helperText={errors.password}
              InputProps={{
                startAdornment: (
                  <LockIcon sx={{ mr: 1, color: "action.active" }} />
                ),
                sx: {
                  fontFamily:
                    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                  fontSize: "14px",
                },
              }}
              variant="outlined"
              sx={{ mb: 2 }}
            />

            {showError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                Credenciales incorrectas. Por favor, intente nuevamente.
              </Alert>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isLoading}
              sx={{
                mt: 2,
                mb: 2,
                py: 1.5,
                bgcolor: "#2563EB",
                "&:hover": { bgcolor: "#1D4ED8" },
                textTransform: "none",
                fontSize: "14px",
                fontWeight: "500",
                fontFamily:
                  "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
              }}
            >
              {isLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                "Iniciar Sesión"
              )}
            </Button>

            <Box sx={{ textAlign: "center" }}>
              <Typography
                variant="body2"
                sx={{
                  fontSize: "14px",
                  fontWeight: "400",
                  fontFamily:
                    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                }}
              >
                ¿No tienes una cuenta?{" "}
                <Link href="/auth/sign" style={{ textDecoration: "none" }}>
                  <Typography
                    component="span"
                    sx={{
                      color: "#2563EB",
                      fontSize: "14px",
                      fontWeight: "500",
                      fontFamily:
                        "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                    }}
                  >
                    Regístrate aquí
                  </Typography>
                </Link>
              </Typography>
            </Box>
          </form>
        </Paper>
      </Box>
    </Box>
  );
};

export default LoginForm;
