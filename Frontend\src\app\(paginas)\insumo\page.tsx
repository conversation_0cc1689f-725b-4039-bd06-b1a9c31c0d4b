"use client";
import React, { useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
  Grid,
  TextField,
  Skeleton,
  Box,
  Tabs,
  Tab,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  InputAdornment,
  FormControl,
  Select,
  MenuItem,
  CircularProgress,
  DialogContentText,
} from "@mui/material";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import CloseIcon from "@mui/icons-material/Close";
import Image from "next/image";
import { validateForm } from "../../../utiles/validarFormFields";
import { useEffect } from "react";
import SearchIcon from "@mui/icons-material/Search";
import BarcodeIcon from "@mui/icons-material/QrCodeOutlined";
import { Inter } from "next/font/google";
const inter = Inter({ subsets: ["latin"] });

interface Categoria {
  categoria: string;
  icon: JSX.Element;
  subProductos: Array<{
    nombre: string;
    icon: JSX.Element;
  }>;
}

const Productos = ({}) => {
  const [estadoModal, setEstadoModal] = useState<"add" | "update">("add");
  const [isSearchBarOpen, setIsSearchBarOpen] = useState(false);
  const [open, setOpen] = useState(false);
  const [filteredRows, setFilteredRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [rows, setRows] = useState([]);
  const [categoriaSeleccionada, setCategoriaSeleccionada] =
    useState<Categoria | null>(null);
  const [subProductoSeleccionado, setSubProductoSeleccionado] = useState("");
  const [loading, setLoading] = useState(true);
  const [loadingSubProductos, setLoadingSubProductos] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [savedProducts, setSavedProducts] = useState<{
    [key: string]: { name: string; brand: string; code: string }[];
  }>({});
  const [searchTerm, setSearchTerm] = useState("");
  const [error, setError] = useState<{ [key: string]: string }>({});
  const [codeType, setCodeType] = useState<"ean13" | "code128">("ean13");
  const selectProductoRef = useRef<HTMLInputElement | HTMLSelectElement>(null);
  const selectSubProductoRef = useRef<HTMLInputElement | HTMLSelectElement>(
    null
  );

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 4000);
  }, []);

  useEffect(() => {
    if (categoriaSeleccionada) {
      setLoadingSubProductos(true);
      setTimeout(() => {
        setLoadingSubProductos(false);
      }, 2000);
    }
  }, [categoriaSeleccionada]);

  const handleCategoriaChange = (categoria: Categoria) => {
    setCategoriaSeleccionada(categoria);
    setSubProductoSeleccionado("");
  };

  const handleListItemClick = (subProductoNombre: string) => {
    setSubProductoSeleccionado(subProductoNombre);
  };

  const [formData, setFormData] = useState({
    productName: "",
    productType: "",
    productBrand: "",
    productCode: "",
  });

  const clearForm = () => {
    setFormData({
      productName: "",
      productType: "",
      productBrand: "",
      productCode: "",
    });
    setError({});
  };

  const errors = validateForm(formData, {
    productName: {
      valid: (v: string) => /^[a-zA-Z\s]+$/.test(v),
      error: "Introduzca el Nombre",
    },
    productBrand: {
      valid: (v: string) => /^[a-zA-Z\s]+$/.test(v),
      error: "Introduzca la Marca",
    },
    productCode: {
      valid: (v: string) => (codeType === "ean13" ? isEAN13(v) : isCode128(v)),
      error:
        codeType === "ean13"
          ? "Introduzca un código EAN-13 válido (debe comenzar con 779)"
          : "Introduzca un código Code 128 válido",
    },
  });

  const handleOpenAdd = () => {
    setEstadoModal("add");
    clearForm();
    setOpen(true);
  };

  const handleClickClose = (
    _event: React.MouseEvent<HTMLElement>,
    reason?: string
  ) => {
    if (reason && reason === "backdropClick") return;
    setOpen(false);
  };

  const handleSearchClick = () => {
    setIsSearchBarOpen(!isSearchBarOpen);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const columns = [
    {
      headerName: "nombre", // Group name
      children: [
        {
          field: "nombre",
          headerName: "Nombre",
          width: 165,
          headerClassName: "custom-header",
        },
        {
          field: "tipo",
          headerName: "Tipo",
          width: 275,
          headerClassName: "custom-header",
        },
      ],
    },
    { field: "marca", headerName: "Marca Comercial", width: 200 },
    { field: "codigo", headerName: "Código de Barras", width: 150 },
  ];

  const agroquimicos = [
    {
      categoria: "Control de Plagas",

      subProductos: [
        {
          nombre: "Acaricidas",
        },
        {
          nombre: "Insecticidas",
        },
        {
          nombre: "Fungicidas",
        },
        {
          nombre: "Herbicidas",
        },
        {
          nombre: "Bactericidas",
        },
      ],
    },

    {
      categoria: "Tratamientos de Semillas",

      subProductos: [
        {
          nombre: "Curasemillas",
        },
        {
          nombre: "Terápicos de Semillas",
        },
        {
          nombre: "Tratamiento de semillas",
        },
      ],
    },

    {
      categoria: "Fertilizantes",

      subProductos: [
        {
          nombre: "Fertilizantes",
        },
        {
          nombre: "Biofertilizante",
        },
        {
          nombre: "Bioestimulante",
        },
        {
          nombre: "Promotor de crecimiento",
        },
      ],
    },

    {
      categoria: "Agentes Biológicos",

      subProductos: [
        {
          nombre: "Biológicos",
        },
        {
          nombre: "Inhibidores de síntesis de lípidos",
        },
      ],
    },

    {
      categoria: "Otros",

      subProductos: [
        {
          nombre: "Coadyuvantes",
        },
        {
          nombre: "Estabilizador de nitrógeno",
        },
        {
          nombre: "Feromonas",
        },
        {
          nombre: "Fitoregulador",
        },
        {
          nombre: "Potenciadores",
        },
      ],
    },
  ];

  // AGREGAR INSUMO
  const handleAddProduct = async () => {
    const insumo = `${subProductoSeleccionado} - ${formData.productName}`;
    const newProduct = {
      insumo: insumo,
      marca: formData.productBrand,
      codigo: formData.productCode,
    };

    const errors = validateForm(formData);
    if (errors) {
      setError(errors);
      return;
    }

    try {
      // Endpoint no implementado - simular éxito temporalmente
      console.warn(
        "⚠️ Endpoint POST /api/insumo no implementado - simulando éxito"
      );

      // Simular una respuesta exitosa
      const mockResponse = { ok: true, status: 200 };

      if (!mockResponse.ok) {
        throw new Error("Error al guardar el insumo");
      }

      // Actualizar el estado de productos guardados
      setSavedProducts((prev) => ({
        ...prev,
        [subProductoSeleccionado]: [
          ...(prev[subProductoSeleccionado] || []),
          {
            name: formData.productName,
            brand: formData.productBrand,
            code: formData.productCode,
          },
        ],
      }));

      clearForm();
      const dataProducts = await fetchInsumos();
      setRows(dataProducts);
      setOpen(false);
    } catch (error) {
      console.error("Error en la solicitud:", error);
    }
  };

  const fetchInsumos = async () => {
    try {
      // Endpoint no implementado - retornar datos mock temporalmente
      console.warn(
        "⚠️ Endpoint /api/insumo no implementado - retornando array vacío"
      );
      return [];
    } catch (error) {
      console.error("Error en la solicitud:", error);
      // Devolver un valor predeterminado en caso de error
      return [];
    }
  };

  useEffect(() => {
    const getData = async () => {
      const dataInsumos = await fetchInsumos();
      // setRows(dataInsumos);
    };

    getData();
  }, []);

  useEffect(() => {
    const loadSavedProducts = async () => {
      try {
        const products = await fetchInsumos();
        // Organizar productos por tipo
        const organized = products.reduce(
          (
            acc: {
              [key: string]: Array<{
                name: string;
                brand: string;
                code: string;
              }>;
            },
            product: {
              typeProduct: string;
              nameProduct: string;
              brandProduct: string;
              codeProduct: string;
            }
          ) => {
            const type = product.typeProduct;
            if (!acc[type]) acc[type] = [];
            acc[type].push({
              name: product.nameProduct,
              brand: product.brandProduct,
              code: product.codeProduct,
            });
            return acc;
          },
          {}
        );
        setSavedProducts(organized);
      } catch (error) {
        console.error("Error cargando productos:", error);
      }
    };

    loadSavedProducts();
  }, []);

  //BUSCAR INSUMOS
  const handleSearchInsumos = async (value: string): Promise<void> => {};

  // CLICK BOTÓN EDITAR
  const handleEdit = async (row: any) => {
    setSelectedRow(row);
    setEstadoModal("update");

    // Extraer el tipo de producto y nombre del campo insumo
    const [tipo, nombre] = row.insumo.split(" - ");

    setFormData({
      productName: nombre,
      productType: tipo,
      productBrand: row.marca,
      productCode: row.codigo,
    });
  };

  // MODIFICAR INSUMO PARA GUARDAR
  const handleUpdateProduct = async () => {
    if (!selectedRow) return;

    const insumo = `${subProductoSeleccionado} - ${formData.productName}`;
    const updatedProduct = {
      insumo: insumo,
      marca: formData.productBrand,
      codigo: formData.productCode,
    };

    try {
      // Endpoint no implementado - simular éxito temporalmente
      console.warn(
        "⚠️ Endpoint PUT /api/insumo no implementado - simulando éxito"
      );

      // Simular una respuesta exitosa
      const mockResponse = { ok: true, status: 200 };

      if (!mockResponse.ok) {
        throw new Error("Error al actualizar el insumo");
      }

      // Actualizar el estado local
      setSavedProducts((prev) => {
        const updated = { ...prev };
        const productIndex = updated[subProductoSeleccionado]?.findIndex(
          (p) => p.code === (selectedRow as { codigo: string }).codigo
        );

        if (productIndex !== undefined && productIndex !== -1) {
          updated[subProductoSeleccionado][productIndex] = {
            name: formData.productName,
            brand: formData.productBrand,
            code: formData.productCode,
          };
        }

        return updated;
      });

      clearForm();
      setEstadoModal("add");
      const dataProducts = await fetchInsumos();
      setRows(dataProducts);
      setSelectedRow(null);
    } catch (error) {
      console.error("Error en la solicitud:", error);
    }
  };

  const labelStyles = {
    fontWeight: 600,
    color: "#333",
    marginBottom: "8px",
    display: "block",
    fontFamily: "Lexend, sans-serif", // Labels en Lexend
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          mb: 3,
          mt: 3,
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="div"
            sx={{ fontWeight: "bold", fontFamily: "Lexend, sans-serif" }}
          >
            Insumos Agricolas
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              color: "text.secondary",
              mt: 1,
              fontFamily: `${inter.style.fontFamily}`,
            }}
          >
            Gestione los Insumos Agricolas
          </Typography>
        </Box>
        <Button
          variant="contained"
          onClick={handleOpenAdd}
          sx={{
            bgcolor: "#2E7D32", // Color verde más oscuro
            color: "#ffffff",
            "&:hover": { bgcolor: "#0D9A0A" },
            height: "fit-content",
            alignSelf: "center",
            fontFamily: `${inter.style.fontFamily}`, // Agregado para usar Inter
          }}
          startIcon={<AddOutlinedIcon />}
        >
          Nuevo Insumo
        </Button>
      </Box>

      <Paper elevation={2} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Buscar..."
          value={searchTerm}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleSearchInsumos(e.target.value)
          }
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <IconButton onClick={handleSearchClick}>
                  <SearchIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />
      </Paper>

      <Dialog open={open} onClose={handleClickClose} maxWidth="md" fullWidth>
        <Box sx={{ p: 3, position: "relative" }}>
          {/* Botón X para cerrar */}
          <IconButton
            aria-label="close"
            onClick={handleClickClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>

          <Box>
            <DialogTitle
              sx={{
                p: 0,
                fontFamily: "Lexend, sans-serif",
                fontSize: "1.5rem",
                fontWeight: "bold",
                color: "#333",
                pr: 6, // Añadir padding derecho para evitar superposición con el botón X
              }}
            >
              Registrar nuevo insumo
            </DialogTitle>
            <DialogContentText
              sx={{
                p: 0,
                mt: 1,
                mb: 3,
                fontFamily: "Inter, sans-serif",
                color: "#666",
              }}
            >
              Complete la información del nuevo insumo a registrar.
            </DialogContentText>
          </Box>
          <DialogContent sx={{ p: 0 }}>
            <Box component="form" sx={{ mt: 1 }}>
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="body2" sx={labelStyles}>
                    Producto
                  </Typography>
                  <FormControl fullWidth>
                    <Select
                      name="productType"
                      labelId="demo-simple-select-label"
                      value={formData.productType}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          productType: e.target.value,
                          productName: "", // Reset subproducto when producto changes
                        });
                        const categoria = agroquimicos.find(
                          (c) => c.categoria === e.target.value
                        );
                        if (categoria) {
                          handleCategoriaChange({
                            ...categoria,
                            icon: <></>,
                            subProductos: categoria.subProductos.map((sub) => ({
                              ...sub,
                              icon: <></>,
                            })),
                          });
                        }
                      }}
                      required
                      inputRef={selectProductoRef}
                      displayEmpty
                      renderValue={(selected: string) => {
                        if (!selected) {
                          return (
                            <span style={{ fontFamily: "Inter, sans-serif" }}>
                              Seleccione un Producto
                            </span>
                          );
                        }
                        return (
                          <span style={{ fontFamily: "Inter, sans-serif" }}>
                            {selected}
                          </span>
                        );
                      }}
                      sx={{
                        "& .MuiSelect-select": {
                          fontFamily: "Inter, sans-serif",
                        },
                        "& .MuiMenuItem-root": {
                          fontFamily: "Inter, sans-serif",
                        },
                      }}
                    >
                      {agroquimicos.map((producto) => (
                        <MenuItem
                          key={producto.categoria}
                          value={producto.categoria}
                          sx={{ fontFamily: "Inter, sans-serif" }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <Typography
                              sx={{ fontFamily: "Inter, sans-serif" }}
                            >
                              {producto.categoria}
                            </Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="body2" sx={labelStyles}>
                    Subproducto
                  </Typography>
                  <FormControl fullWidth>
                    <Select
                      value={subProductoSeleccionado}
                      onChange={(e) => handleListItemClick(e.target.value)}
                      error={!!error?.productName}
                      disabled={!formData.productType}
                      required
                      inputRef={selectSubProductoRef}
                      displayEmpty
                      renderValue={(selected: string) => {
                        if (!selected) {
                          return "Seleccione un SubProducto";
                        }
                        return selected;
                      }}
                    >
                      {agroquimicos
                        .find((p) => p.categoria === formData.productType)
                        ?.subProductos.map((sub) => (
                          <MenuItem key={sub.nombre} value={sub.nombre}>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              <Typography>{sub.nombre}</Typography>
                            </Box>
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="body2" sx={labelStyles}>
                    Nombre
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Camicaze A501-X"
                    name="productName"
                    value={formData.productName}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        productName: e.target.value,
                      })
                    }
                    error={!!error?.productName}
                    helperText={error?.productName}
                    sx={{
                      "& .MuiInputBase-input": {
                        fontFamily: "Inter, sans-serif",
                      },
                      "& .MuiFormHelperText-root": {
                        fontFamily: "Inter, sans-serif",
                      },
                    }}
                  />
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="body2" sx={labelStyles}>
                    Marca
                  </Typography>
                  <TextField
                    placeholder="Syngenta"
                    fullWidth
                    name="productBrand"
                    value={formData.productBrand}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        productBrand: e.target.value,
                      })
                    }
                    sx={{
                      "& .MuiInputBase-input": {
                        fontFamily: "Inter, sans-serif",
                      },
                    }}
                  />
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Typography variant="body2" sx={labelStyles}>
                    Tipo de Código
                  </Typography>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <Select
                      value={codeType}
                      onChange={(e) => {
                        setCodeType(e.target.value as "ean13" | "code128");
                        setFormData({ ...formData, productCode: "" }); // Limpiar el código al cambiar el tipo
                      }}
                      sx={{
                        "& .MuiSelect-select": {
                          fontFamily: "Inter, sans-serif",
                        },
                      }}
                    >
                      <MenuItem
                        value="ean13"
                        sx={{ fontFamily: "Inter, sans-serif" }}
                      >
                        EAN-13
                      </MenuItem>
                      <MenuItem
                        value="code128"
                        sx={{ fontFamily: "Inter, sans-serif" }}
                      >
                        Code 128
                      </MenuItem>
                    </Select>
                  </FormControl>

                  <Typography variant="body2" sx={labelStyles}>
                    {codeType === "ean13" ? "Código EAN-13" : "Código Code 128"}
                  </Typography>
                  <TextField
                    placeholder={
                      codeType === "ean13"
                        ? "779XXXXXXXXXX"
                        : "Código alfanumérico"
                    }
                    fullWidth
                    name="productCode"
                    value={formData.productCode}
                    onChange={(e) => {
                      let value = e.target.value;
                      if (codeType === "ean13") {
                        value = value.replace(/[^\d]/g, "").slice(0, 13);
                      } else {
                        value = value.slice(0, 48);
                      }
                      setFormData({
                        ...formData,
                        productCode: value,
                      });
                    }}
                    error={!!error?.productCode}
                    helperText={
                      error?.productCode ||
                      (codeType === "ean13"
                        ? "Formato: 779XXXXXXXXXX (13 dígitos)"
                        : "Formato: Alfanumérico (máx. 48 caracteres)")
                    }
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <BarcodeIcon sx={{ color: "action.active" }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiInputBase-input": {
                        fontFamily: "Inter, sans-serif",
                      },
                      "& .MuiFormHelperText-root": {
                        fontFamily: "Inter, sans-serif",
                      },
                    }}
                  />
                </Grid>
              </Grid>
              <Box sx={{ mt: 3, display: "flex", justifyContent: "flex-end" }}>
                <Button
                  variant="contained"
                  type="submit"
                  onClick={
                    estadoModal === "add"
                      ? handleAddProduct
                      : handleUpdateProduct
                  }
                >
                  {estadoModal === "add" ? "Agregar" : "Guardar"}
                </Button>
              </Box>
            </Box>
          </DialogContent>
        </Box>
      </Dialog>

      <Paper elevation={2} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}
        >
          {agroquimicos.map((categoria, index) => (
            <Tab
              key={index}
              label={
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography
                    sx={{
                      textTransform: "capitalize",
                      fontSize: "0.875rem",
                      fontFamily: "Lexend, sans-serif",
                    }}
                  >
                    {categoria.categoria}
                  </Typography>
                </Box>
              }
              sx={{ textTransform: "none" }}
            />
          ))}
        </Tabs>

        {agroquimicos.map((categoria, index) => (
          <div key={index} role="tabpanel" hidden={tabValue !== index}>
            {tabValue === index && (
              <Box sx={{ p: 2 }}>
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    fontFamily: "Lexend, sans-serif",
                    fontWeight: "600",
                  }}
                >
                  Subproductos de {categoria.categoria}
                </Typography>
                <Grid container spacing={2}>
                  {loading ? (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        width: "100%",
                        mt: 3,
                      }}
                    >
                      <CircularProgress />
                    </Box>
                  ) : (
                    categoria.subProductos.map((subProducto, subIndex) => {
                      const productosGuardados =
                        savedProducts[subProducto.nombre];
                      return (
                        <Grid size={{ xs: 12, sm: 6, md: 4 }} key={subIndex}>
                          <Card
                            sx={{
                              height: "100%",
                              display: "flex",
                              flexDirection: "column",
                            }}
                          >
                            <CardHeader
                              title={
                                <Typography
                                  variant="h6"
                                  sx={{
                                    fontSize: "1rem",
                                    fontFamily: "Lexend, sans-serif",
                                  }}
                                >
                                  {subProducto.nombre}
                                </Typography>
                              }
                              sx={{ pb: 1 }}
                            />
                            <CardContent sx={{ pt: 0, flex: 1 }}>
                              {productosGuardados?.length > 0 ? (
                                <Box>
                                  {productosGuardados.map(
                                    (producto, prodIndex) => (
                                      <Typography
                                        key={prodIndex}
                                        sx={{
                                          mb: 1,
                                          fontSize: "0.875rem",
                                          fontFamily: "Inter, sans-serif",
                                          color: "text.secondary",
                                        }}
                                      >
                                        • {producto.name}
                                      </Typography>
                                    )
                                  )}
                                </Box>
                              ) : (
                                <Typography
                                  sx={{
                                    fontSize: "0.875rem",
                                    fontFamily: "Inter, sans-serif",
                                    color: "text.secondary",
                                    fontStyle: "italic",
                                  }}
                                >
                                  No hay productos registrados
                                </Typography>
                              )}
                            </CardContent>
                          </Card>
                        </Grid>
                      );
                    })
                  )}
                </Grid>
              </Box>
            )}
          </div>
        ))}
      </Paper>
    </>
  );
};
export default Productos;
function isEAN13(v: string): boolean {
  throw new Error("Function not implemented.");
}

function isCode128(v: string): boolean {
  throw new Error("Function not implemented.");
}
