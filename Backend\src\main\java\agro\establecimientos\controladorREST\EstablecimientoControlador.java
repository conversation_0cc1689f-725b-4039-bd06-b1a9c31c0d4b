package agro.establecimientos.controladorREST;

import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import agro.establecimientos.EstablecimientoCampo;
import agro.establecimientos.dto.EstablecimientoDto;
import agro.establecimientos.modeloEntidades.EstablecimientoEntidad;
import agro.lotes.dto.LoteDto;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/establecimiento")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")

public class EstablecimientoControlador {
    private final EstablecimientoCampo establecimientoCampo;

    @PostMapping
    public ResponseEntity<?> guardarEstablecimiento(@RequestBody EstablecimientoDto establecimientoNuevo) {
        try {
            System.out.println("📌 Datos recibidos en el backend:");
            System.out.println("Nombre: " + establecimientoNuevo.getNombre());
            System.out.println("Persona ID: " + establecimientoNuevo.getPersona_id());
            System.out.println("Lugar: " + establecimientoNuevo.getLugar());
            
            if (establecimientoNuevo.getLotes() != null) {
                System.out.println("Lotes recibidos: " + establecimientoNuevo.getLotes().size());
                for (LoteDto lote : establecimientoNuevo.getLotes()) {
                    System.out.println("  - Lote: " + lote.getNombre() + ", Superficie: " + lote.getSuperficie());
                    System.out.println("  - Coordenadas: " + lote.getCoordenadas());
                }
            } else {
                System.out.println("No se recibieron lotes");
            }

            EstablecimientoEntidad savedEstablecimiento = establecimientoCampo.guardarEstablecimiento(establecimientoNuevo);
            return ResponseEntity.ok(savedEstablecimiento);
        } catch (IllegalArgumentException e) {
            System.err.println("Error de validación: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest()
                    .body(Map.of(
                            "error", "Error de validación",
                            "mensaje", e.getMessage()));
        } catch (Exception e) {
            System.err.println("Error general: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest()
                    .body(Map.of(
                            "error", "Error al guardar establecimiento",
                            "mensaje", e.getMessage(),
                            "detalles", e.getClass().getSimpleName()));
        }
    }

    @GetMapping
    public List<EstablecimientoDto> getListaEstablecimientos() {
        return establecimientoCampo.listaEstablecimiento();
    }

    @PutMapping
    public ResponseEntity<?> actualizarEstablecimiento(@RequestBody EstablecimientoDto establecimientoActualizado) {
        try {
            System.out.println("📌 Datos recibidos para actualización:");
            System.out.println("ID: " + establecimientoActualizado.getId());
            System.out.println("Nombre: " + establecimientoActualizado.getNombre());
            System.out.println("Persona ID: " + establecimientoActualizado.getPersona_id());
            System.out.println("Lugar: " + establecimientoActualizado.getLugar());
            
            if (establecimientoActualizado.getLotes() != null) {
                System.out.println("Lotes recibidos: " + establecimientoActualizado.getLotes().size());
                for (LoteDto lote : establecimientoActualizado.getLotes()) {
                    System.out.println("  - Lote: " + lote.getNombre() + ", Superficie: " + lote.getSuperficie());
                }
            } else {
                System.out.println("No se recibieron lotes");
            }

            EstablecimientoEntidad updatedEstablecimiento = establecimientoCampo.actualizarEstablecimiento(establecimientoActualizado);
            return ResponseEntity.ok(updatedEstablecimiento);
        } catch (IllegalArgumentException e) {
            System.err.println("Error de validación: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest()
                    .body(Map.of(
                            "error", "Error de validación",
                            "mensaje", e.getMessage()));
        } catch (Exception e) {
            System.err.println("Error general: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest()
                    .body(Map.of(
                            "error", "Error al actualizar establecimiento",
                            "mensaje", e.getMessage(),
                            "detalles", e.getClass().getSimpleName()));
        }
    }
}
