package agro.establecimientos.repositorioJPA;

import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import agro.establecimientos.modeloEntidades.EstablecimientoEntidad;

@Repository
public interface EstablecimientoRepositorio extends JpaRepository<EstablecimientoEntidad, UUID> {
    boolean existsByNombre(String nombre);
}
