// import { Typography } from "@mui/material";
// import { Box } from "@mui/system";

// const CustomTooltip = ({ payload, label }) => {
//   // Formatear el número
//   const formatValue = (value, type) => {
//     if (type === "currency") {
//       return new Intl.NumberFormat("es-ES", {
//         style: "currency",
//         currency: "$",
//         maximumFractionDigits: 0,
//       }).format(value);
//     } else if (type === "percentage") {
//       return `${value}%`;
//     } else {
//       return new Intl.NumberFormat("es-ES", {
//         style: "decimal",
//         minimumFractionDigits: 2,
//         maximumFractionDigits: 2,
//       }).format(value);
//     }
//   };

//   if (!payload || payload.length === 0) return null;

//   return (
//     <Box
//       sx={{
//         position: "absolute", // Asegura que el tooltip sea posicionado dinámicamente
//         top: `${payload[0]?.coordinateY || 0}px`, // Ajusta la posición si es necesario
//         left: `${payload[0]?.coordinateX || 0}px`,
//         padding: 2,
//         backgroundColor: "#fff",
//         borderRadius: "4px",
//         boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
//         border: "2px solid black",
//         opacity: 0,
//         animation: "fadeIn 0.3s forwards", // Animación de desvanecimiento
//       }}
//     >
//       <style>{`
//         @keyframes fadeIn {
//           from {
//             opacity: 0;
//           }
//           to {
//             opacity: 1;
//           }
//         }
//       `}</style>
//       {/* Título del tooltip */}
//       <Typography
//         variant="subtitle2"
//         color="textSecondary"
//         sx={{
//           fontSize: "16px",
//           fontWeight: "bold",
//           textTransform: "uppercase", // Para darle un toque más estilizado
//           letterSpacing: "1px",
//         }}
//       >
//         {label}
//       </Typography>
//       {/* Mostrar los valores del tooltip */}
//       <Box>
//         {payload.map((entry, index) => (
//           <Typography
//             key={index}
//             sx={{
//               color: entry.color,
//               fontSize: "18px", // Aumentar el tamaño de la fuente
//               fontWeight: "bold", // Hacer la fuente en negrita
//             }}
//           >
//             {entry.name}: {formatValue(entry.value, entry.type)}{" "}
//             {/* Asumimos que `type` se pasa en el `payload` */}
//           </Typography>
//         ))}
//       </Box>
//     </Box>
//   );
// };

// export default CustomTooltip;
