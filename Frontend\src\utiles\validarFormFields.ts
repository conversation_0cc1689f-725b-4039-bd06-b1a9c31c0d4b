export interface CustomValid {
  [fieldName: string]: {
    valid: (val: any) => boolean;
    error: string;
  };
}
const isAlpha = (val: string) => /^[A-Za-z\s]+$/.test(val); // Letras y espacios
const isNumeric = (val: string) => /^[0-9]+$/.test(val); //Numeros
const isAlphaNumeric = (val: string) => /^[A-Za-z0-9\s]+$/.test(val); //Letras y Numeros
const isEmail = (val: string) => /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/.test(val); // Formato mail ejemplo@gmail/hotmail.com
const isPhoneNumber = (val: string) => /^\(\d{4}\)-\d{6}$/.test(val); // Formato (9999)-999999
const isDocumento = (val: string) => /^\d{2}-\d{8}-\d{1}$/.test(val); // Formato 99-99999999-9

// Validación para EAN-13
const isEAN13 = (val: string) => {
  // Verifica si tiene 13 dígitos y comienza con 779 (prefijo argentino)
  if (!/^779\d{10}$/.test(val)) return false;

  // Cálculo del dígito verificador
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(val[i]) * (i % 2 === 0 ? 1 : 3);
  }
  const checkDigit = (10 - (sum % 10)) % 10;
  
  // Verifica si el último dígito coincide con el dígito verificador calculado
  return parseInt(val[12]) === checkDigit;
};

// Validación para Code 128
const isCode128 = (val: string) => {
  // Code 128 permite caracteres alfanuméricos y algunos caracteres especiales
  // Longitud entre 1 y 48 caracteres
  return /^[A-Za-z0-9!@#$%^&*()_+\-=\[\]{};:,.<>?]{1,48}$/.test(val);
};

export const customValidations = {
  letras: { valid: isAlpha, error: "El campo debe contener solo letras." },
  numeros: { valid: isNumeric, error: "El campo debe contener solo números." },
  letrasNumeros: {
    valid: isAlphaNumeric,
    error: "El campo debe contener solo letras y números.",
  },
  mail: {
    valid: isEmail,
    error: "Por favor ingrese un formato de correo electrónico válido.",
  },
  telefono: {
    valid: isPhoneNumber,
    error: "El formato de teléfono debe ser (9999)-999999.",
  }, // Validación de teléfono
  documento: {
    valid: isDocumento,
    error: "El formato de documento debe ser 99-99999999-9.",
  }, // Validación de documento
  ean13: {
    valid: isEAN13,
    error: "El código debe ser EAN-13 válido (formato: 779XXXXXXXXXX)",
  },
  code128: {
    valid: isCode128,
    error: "El código debe ser Code 128 válido (alfanumérico, máx. 48 caracteres)",
  },
};
// Validación para campos individuales
export const validateField = (
  value: string,
  validationType: string
): string | undefined => {
  if (!value || value.trim() === "") {
    return "Campo requerido"; // Mensaje de error si el campo está vacío
  }
  return undefined; // Si no hay error, devuelve undefined
};

// Validación para todo el formulario
export const validateForm = (
  data: any,
  customValid?: CustomValid
): any | undefined => {
  const errors: any = {};

  Object.keys(data).forEach((field) => {
    const value = data[field];

    if (value === undefined || value === null || value === "") {
      errors[field] = "Campo requerido";
    } else if (customValid && customValid[field]) {
      // Aplicar validación personalizada
      const isValid = customValid[field].valid(value);
      if (!isValid) {
        errors[field] = customValid[field].error;
      }
    }
  });

  return Object.keys(errors).length === 0 ? undefined : errors;
};
