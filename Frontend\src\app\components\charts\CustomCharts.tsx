import { Card, CardContent } from "@mui/material";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Pie, Cell, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, LabelList } from "recharts";

type ChartType = "bar" | "line" | "pie";

interface CustomChartProps {
  title: string;
  type: ChartType;
  data: any[];
  dataKey: string;
  nameKey: string;
  colors?: string[];
  height?: number;
  showValues?: boolean;
}

export function CustomChart({
  title,
  type,
  data,
  dataKey,
  nameKey,
  colors = [
    "#4CAF50", "#2196F3", "#FFC107", "#E91E63",
    "#9C27B0", "#FF5722", "#795548", "#607D8B"
  ],
  height = 300,
  showValues = false,
}: CustomChartProps) {
  const renderChart = () => {
    switch (type) {
      case "bar":
        return (
          <ResponsiveContainer width="100%" height={height}>
            <Bar<PERSON>hart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis 
                dataKey={nameKey} 
                tick={{ fontSize: 12 }}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip 
                formatter={(value) => [`${value}`, nameKey]} // Formato personalizado
                labelFormatter={(label) => `${label}`} // Nombre del lote
              />
              <Legend />
              <Bar dataKey={dataKey} name={title} radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
                {showValues && (
                  <LabelList
                    dataKey={dataKey}
                    position="top"
                    formatter={(value) => `${value}`}
                  />
                )}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        );
      case "line":
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey={nameKey} tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey={dataKey} 
                stroke={colors[0]} 
                strokeWidth={2} 
                dot={{ r: 4 }}
              >
                {showValues && (
                  <LabelList
                    dataKey={dataKey}
                    position="top"
                    formatter={(value) => `${value}`}
                  />
                )}
              </Line>
            </LineChart>
          </ResponsiveContainer>
        );
      case "pie":
        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={80}
                fill="#8884d8"
                dataKey={dataKey}
                nameKey={nameKey}
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value, name) => [`${value}`, name]}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        );
      default:
        return null;
    }
  };

  return (
    <Card
      sx={{
        border: '2px solid #1F8EEB',
        borderRadius: '8px',
        backgroundColor: '#ffffff',
        overflow: 'hidden',
        height: '100%',
      }}
    >
      <CardContent>
        <h3 style={{ marginBottom: '1rem' }}>{title}</h3>
        {renderChart()}
      </CardContent>
    </Card>
  );
}
