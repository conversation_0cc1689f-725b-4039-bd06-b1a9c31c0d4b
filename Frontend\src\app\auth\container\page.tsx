"use client";
// import React, { useState } from "react";
// import { Tabs, Tab, Paper, Typography } from "@mui/material";
// import IniciarSesion from "../login/page";
// import Registar from "../sign/page";
// import RecuperarPassword from "../recuperar/page";
// import IconoPersonalizado from "../../components/icon/IconoPersonalizado";
// import "../../components/animacion/animacion.css";
// const Contenedorsesiónsalida = () => {
//   const [value, setValue] = useState<number>(0);
//   const [registroHabilitado, setRegistroHabilitado] = useState(false);
//   const [recuperarPasswordHabilitado, setRecuperarPasswordHabilitado] =
//     useState(false);

//   const paperStyle: React.CSSProperties = {
//     padding: 20,
//     height: "70vh",
//     width: 550,
//     margin: "0px auto",
//     border: "1px solid black",
//     fontWeight: "bold",
//     backgroundColor: "#F5F5F5",
//   };

//   const handleRegistroClick = () => {
//     setRegistroHabilitado(true);
//     setValue(1); // Cambiar a la pestaña de "Inscribirse"
//   };

//   const handleRecuperarClick = () => {
//     setRecuperarPasswordHabilitado(true);
//     setValue(2); // Cambiar a la pestaña de "Recuperar Contraseña"
//   };

//   return (
//     <>
//       <div
//         style={{
//           display: "flex",
//           alignItems: "center",
//           justifyContent: "center",
//           marginTop: 100,
//         }}
//       >
//         <div className="contenedor">
//           <Typography
//             variant="h5"
//             sx={{
//               fontWeight: "bold",
//               color: "#2E7D32",
//               fontFamily: "'Roboto', sans-serif",
//               textShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
//             }}
//           >
//             Agro Contratistas
//           </Typography>
//         </div>
//         <div
//           style={{
//             display: "flex",
//             flexDirection: "column",
//             alignItems: "center",
//           }}
//         >
//           <IconoPersonalizado
//             icono="logotractor.png"
//             width={64}
//             height={64}
//             style={{
//               marginRight: 10,
//               filter: "drop-shadow(2px 4px 6px rgba(0,0,0,0.2))",
//             }}
//           />
//         </div>
//       </div>

//       <Paper elevation={20} style={paperStyle}>
//         <Tabs
//           value={value}
//           indicatorColor="primary"
//           textColor="primary"
//           aria-label="disabled tabs example"
//           onChange={(event, newValue) => setValue(newValue)}
//           style={{ width: "100%" }} // Ensure Tabs take full width
//         >
//           <Tab
//             label={
//               <Typography
//                 variant="subtitle2"
//                 style={{ textTransform: "none", fontSize: "1rem" }}
//               >
//                 Iniciar Sesión
//               </Typography>
//             }
//           />
//           <Tab
//             label={
//               <Typography
//                 variant="subtitle2"
//                 style={{ textTransform: "none", fontSize: "1rem" }}
//               >
//                 Inscribirse
//               </Typography>
//             }
//             disabled={!registroHabilitado}
//           />
//           <Tab
//             label={
//               <Typography
//                 variant="subtitle2"
//                 style={{ textTransform: "none", fontSize: "1rem" }}
//               >
//                 Recuperar Contraseña
//               </Typography>
//             }
//             disabled={!recuperarPasswordHabilitado}
//           />
//         </Tabs>

//         <div style={{ marginTop: 45 }}>
//           {value === 0 && (
//             <IniciarSesion
//               activarInscribirse={handleRegistroClick}
//               activarRecuperarPassword={handleRecuperarClick}
//             />
//           )}
//           {value === 1 && <Registar />}
//           {value === 2 && <RecuperarPassword goToLogin={() => setValue(0)} />}
//         </div>
//       </Paper>
//     </>
//   );
// };

// export default Contenedorsesiónsalida;

"use client";

import React from "react";
import Link from "next/link";
import {
  Button,
  Box,
  Typography,
  Container,
  Paper,
  Divider,
} from "@mui/material";
import Image from "next/image";

const Index = () => {
  return (
    <Box
      sx={{
        minHeight: "100vh",
        width: "100%",
        position: "relative",
        bgcolor: "grey.100",
      }}
    >
      {/* Background Image Container */}
      <Box
        sx={{
          position: "fixed",
          inset: 0,
          width: "100%",
          height: "100%",
          zIndex: 0,
          "&::before": {
            content: '""',
            position: "absolute",
            inset: 0,
            background:
              "linear-gradient(to right, rgba(30,64,175,0.4), rgba(22,101,52,0.4))",
            mixBlendMode: "multiply",
            zIndex: 1,
          },
        }}
      >
        <Image
          src="/assets/img/fondo.jpg"
          alt="Farm landscape"
          fill
          sizes="100vw"
          style={{
            objectFit: "cover",
          }}
          quality={100}
          priority
        />
      </Box>

      {/* Content Container */}
      <Container
        maxWidth="sm"
        sx={{
          position: "relative",
          zIndex: 1,
          minHeight: "100vh",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          px: 2,
        }}
      >
        <Box sx={{ textAlign: "center", mb: 4 }}>
          <Typography
            variant="h3"
            component="h1"
            sx={{
              color: "white",
              fontWeight: "bold",
              textShadow: "2px 2px 4px rgba(0,0,0,0.3)",
              mb: 1,
              fontFamily: "var(--font-roboto)",
            }}
          >
            AgroServicios
          </Typography>
          <Typography
            variant="h6"
            sx={{
              color: "grey.100",
              textShadow: "1px 1px 2px rgba(0,0,0,0.3)",
              fontFamily: "var(--font-sans)",
            }}
          >
            Gestión de Servicios Agropecuarios
          </Typography>
        </Box>

        <Paper
          elevation={4}
          sx={{
            p: 4,
            bgcolor: "rgba(255,255,255,0.9)",
            backdropFilter: "blur(8px)",
            borderRadius: 2,
          }}
        >
          <Box sx={{ mb: 3 }}>
            <Link href="/auth/login" style={{ textDecoration: "none" }}>
              <Button
                variant="contained"
                fullWidth
                sx={{
                  py: 1.5,
                  fontSize: "1.1rem",
                  textTransform: "none",
                  bgcolor: "primary.main",
                  "&:hover": {
                    bgcolor: "primary.dark",
                  },
                }}
              >
                Iniciar Sesión
              </Button>
            </Link>
            <Typography
              variant="body2"
              sx={{
                mt: 1,
                color: "text.secondary",
              }}
            >
              ¿Ya tienes una cuenta? Inicia sesión para continuar.
            </Typography>
          </Box>

          <Box sx={{ position: "relative", my: 3 }}>
            <Divider>
              <Typography
                variant="body2"
                sx={{
                  px: 2,
                  color: "text.secondary",
                  bgcolor: "background.paper",
                }}
              >
                O
              </Typography>
            </Divider>
          </Box>

          <Box>
            <Link href="/auth/sign" style={{ textDecoration: "none" }}>
              <Button
                variant="outlined"
                fullWidth
                sx={{
                  py: 1.5,
                  fontSize: "1.1rem",
                  textTransform: "none",
                  borderColor: "primary.main",
                  color: "primary.main",
                  "&:hover": {
                    borderColor: "primary.dark",
                    bgcolor: "rgba(25, 118, 210, 0.04)",
                  },
                }}
              >
                Crear Cuenta
              </Button>
            </Link>
            <Typography
              variant="body2"
              sx={{
                mt: 1,
                color: "text.secondary",
              }}
            >
              ¿No tienes una cuenta? Regístrate para comenzar.
            </Typography>
          </Box>
        </Paper>

     
      </Container>
    </Box>
  );
};

export default Index;
