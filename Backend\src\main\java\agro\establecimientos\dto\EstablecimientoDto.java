package agro.establecimientos.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import agro.lotes.dto.LoteDto;
import agro.personas.dto.PersonaDto;

public class EstablecimientoDto {

    private UUID id;
    private String nombre;
    private UUID persona_id;
    private String lugar;
    private PersonaDto persona;
    private List<LoteDto> lotes = new ArrayList<>();
    private String validationErrors;

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public UUID getPersona_id() {
        return persona_id;
    }

    public void setPersona_id(UUID persona_id) {
        this.persona_id = persona_id;
    }

    public String getLugar() {
        return lugar;
    }

    public void setLugar(String lugar) {
        this.lugar = lugar;
    }

    public PersonaDto getPersona() {
        return persona;
    }

    public void setPersona(PersonaDto persona) {
        this.persona = persona;
    }
    
    public List<LoteDto> getLotes() {
        return lotes;
    }

    public void setLotes(List<LoteDto> lotes) {
        this.lotes = lotes;
    }

    public boolean isValid() {
        StringBuilder errors = new StringBuilder();

        if (nombre == null || nombre.trim().isEmpty()) {
            errors.append("El nombre es requerido. ");
        }

        if (lugar == null || lugar.trim().isEmpty()) {
            errors.append("El lugar es requerido. ");
        }

        if (persona_id == null) {
            errors.append("El ID de persona es requerido. ");
        }

        validationErrors = errors.toString().trim();
        return validationErrors.isEmpty();
    }

    public String getValidationErrors() {
        return validationErrors;
    }
}
