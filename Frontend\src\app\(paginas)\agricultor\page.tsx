"use client";
import React, { useState, useMemo, useCallback } from "react";
import {
  Button,
  Paper,
  TextField,
  Typography,
  Box,
  InputAdornment,
  IconButton,
} from "@mui/material";
import {
  Add as AddOutlinedIcon,
  Search as SearchIcon,
} from "@mui/icons-material";
import Datatable from "../../components/table/DataTable";
import { apiService } from "../../../utiles/apiService";
import { useApiCache } from "../../../utiles/useApiCache";
import { OptimizedSkeleton } from "../../components/loading/OptimizedSkeleton";
import { Inter } from "next/font/google";
import AgricultorModal from "./AgricultorModal";

const inter = Inter({ subsets: ["latin"] });

interface Client {
  id: number;
  propietarioNombre?: string;
  propietarioDocumento?: string;
  propietarioTelefono?: string;
  propietarioEmail?: string;
  razonSocial: string;
  tipoCliente?: string;
  direccion: string;
  empresaLocalidad?: string;
  provincia?: string;
  condFrenteIva: string;
  nombreContacto?: string;
  cargoContacto?: string;
  telefono: string;
  mail: string;
  lugar: string;
  documento: string;
}

const AgricultorGanadero: React.FC = () => {
  // Usar el hook optimizado para cargar datos con caché
  const {
    data: rows = [],
    loading: isLoading,
    error: apiError,
    refetch,
  } = useApiCache<Client[]>(
    "agricultores",
    () =>
      apiService
        .getAgricultores(true)
        .then((res) => (res.success ? (res.data as Client[]) : [])),
    {
      cacheTime: 5 * 60 * 1000, // 5 minutos
      staleTime: 2 * 60 * 1000, // 2 minutos
    }
  );

  const [selectedRow, setSelectedRow] = useState<Client | null>(null);
  const [open, setOpen] = useState(false);
  const [filteredRows, setFilteredRows] = useState<Client[]>([]);
  const [estadoModal, setEstadoModal] = useState<"add" | "update">("add");
  const [searchTerm, setSearchTerm] = useState("");

  // Optimizar columnas con useMemo
  const columns = useMemo(() => [
    {
      field: "empresa",
      headerName: "Empresa",
      width: 280,
      headerClassName: "custom-header",
      renderCell: (params: any) => (
        <Box sx={{ py: 1 }}>
          <Typography variant="body2" sx={{ fontWeight: "600", color: "#333", fontSize: "0.875rem" }}>
            {params.row.razonSocial}
          </Typography>
          {params.row.tipoCliente && (
            <Typography variant="caption" sx={{ color: "#666", fontSize: "0.75rem", fontStyle: "italic" }}>
              {params.row.tipoCliente}
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: "contacto",
      headerName: "Contacto",
      width: 220,
      headerClassName: "custom-header",
      renderCell: (params: any) => (
        <Box sx={{ py: 1 }}>
          {params.row.nombreContacto ? (
            <>
              <Typography variant="body2" sx={{ fontWeight: "600", color: "#333", fontSize: "0.875rem" }}>
                {params.row.nombreContacto}
              </Typography>
              {params.row.cargoContacto && (
                <Typography variant="caption" sx={{ color: "#666", fontSize: "0.75rem", fontStyle: "italic" }}>
                  {params.row.cargoContacto}
                </Typography>
              )}
            </>
          ) : (
            <Typography variant="body2" sx={{ color: "#999", fontStyle: "italic", fontSize: "0.875rem" }}>
              Sin contacto
            </Typography>
          )}
        </Box>
      ),
    },
    { field: "telefono", headerName: "Teléfono", width: 140, headerClassName: "custom-header" },
    { field: "mail", headerName: "Email", width: 180, headerClassName: "custom-header" },
    { field: "lugar", headerName: "Ubicación", width: 200, headerClassName: "custom-header" },
    { field: "documento", headerName: "Documento", width: 140, headerClassName: "custom-header" },
  ], []);

  // Optimizar funciones con useCallback
  const handleOpenAdd = useCallback(() => {
    setEstadoModal("add");
    setSelectedRow(null);
    setOpen(true);
  }, []);

  const handleCloseModal = useCallback((event?: React.MouseEvent<HTMLElement>, reason?: string) => {
    if (reason && reason === "backdropClick") return;
    setOpen(false);
  }, []);

  const handleSearchCliente = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = event.target.value;
    setSearchTerm(searchValue);

    const filteredData = (rows as Client[]).filter((row: Client) => {
      return (
        row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.direccion.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.telefono.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.mail.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.lugar.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.documento.toLowerCase().includes(searchValue.toLowerCase())
      );
    });
    setFilteredRows(filteredData);
  }, [rows]);

  const handleDeleteCliente = useCallback(async (id: number) => {
    try {
      const res = await fetch(`http://localhost:8080/api/agricultor/${id}`, {
        method: "DELETE",
      });
      if (res.ok) {
        refetch();
      }
    } catch (error) {
      console.error("Error al eliminar:", error);
    }
  }, [refetch]);

  const handleEdit = useCallback(async (id: any) => {
    try {
      const res = await fetch(`http://localhost:8080/api/agricultor/${id}`, {
        method: "GET",
      });
      if (res.ok) {
        const agricultor = await res.json();
        setSelectedRow(agricultor);
        setEstadoModal("update");
        setOpen(true);
      }
    } catch (error) {
      console.error("Error al obtener datos:", error);
    }
  }, []);

  const handleSelectAgricultor = useCallback((id: number) => {
    const selectedAgricultor = rows.find((row) => row.id === id);
    if (selectedAgricultor) {
      const agricultor = {
        id: selectedAgricultor.id,
        razonSocial: selectedAgricultor.razonSocial,
      };
      localStorage.setItem("selectedAgricultor", JSON.stringify(agricultor));
      window.location.href = "/establecimiento";
    }
  }, [rows]);

  const handleModalSubmit = useCallback(async (formData: any) => {
    try {
      const url = estadoModal === "add" 
        ? "http://localhost:8080/api/agricultor"
        : "http://localhost:8080/api/agricultor";
      
      const method = estadoModal === "add" ? "POST" : "PUT";
      
      const res = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (res.ok) {
        refetch();
        setOpen(false);
      }
    } catch (error) {
      console.error("Error al guardar:", error);
    }
  }, [estadoModal, refetch]);

  // Mostrar loading
  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <OptimizedSkeleton variant="dashboard" />
      </Box>
    );
  }

  // Mostrar error
  if (apiError) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">Error al cargar los datos: {apiError.message}</Typography>
        <Button onClick={() => refetch()} sx={{ mt: 2 }}>Reintentar</Button>
      </Box>
    );
  }

  return (
    <>
      <Box sx={{ display: "flex", justifyContent: "space-between", mb: 3, mt: 3 }}>
        <Box>
          <Typography variant="h4" component="div" sx={{ fontWeight: "bold", fontFamily: "Lexend, sans-serif" }}>
            Agricultores
          </Typography>
          <Typography variant="subtitle1" sx={{ color: "text.secondary", mt: 1, fontFamily: inter.style.fontFamily }}>
            Gestione la información de sus Agricultores
          </Typography>
        </Box>
        <Button
          variant="contained"
          onClick={handleOpenAdd}
          sx={{
            bgcolor: "#2E7D32",
            color: "#ffffff",
            "&:hover": { bgcolor: "#0D9A0A" },
            height: "fit-content",
            alignSelf: "center",
            fontFamily: inter.style.fontFamily,
          }}
          startIcon={<AddOutlinedIcon />}
        >
          Nuevo Agricultor
        </Button>
      </Box>

      <Paper elevation={2} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Buscar..."
          value={searchTerm}
          onChange={handleSearchCliente}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton>
                    <SearchIcon />
                  </IconButton>
                </InputAdornment>
              ),
            },
          }}
          sx={{ mb: 2 }}
        />
        <Datatable
          columns={columns}
          rows={filteredRows.length > 0 ? filteredRows : Array.isArray(rows) ? rows : []}
          option={true}
          optionDeleteFunction={handleDeleteCliente}
          optionUpdateFunction={handleEdit}
          setSelectedRow={(row) => setSelectedRow(row as Client | null)}
          selectedRow={selectedRow}
          optionSelect={handleSelectAgricultor}
        />
      </Paper>

      <AgricultorModal
        open={open}
        onClose={handleCloseModal}
        onSubmit={handleModalSubmit}
        estadoModal={estadoModal}
        initialData={selectedRow}
      />
    </>
  );
};

export default AgricultorGanadero;
