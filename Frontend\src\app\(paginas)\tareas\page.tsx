"use client";
import { useEffect, useRef, useState } from "react";
import {
  Box,
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Paper,
  TextField,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  DialogActions,
  Card,
  CardContent,
  Chip,
  DialogContentText,
  SelectChangeEvent,
} from "@mui/material";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import SearchIcon from "@mui/icons-material/Search";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import Autocomplete from "@mui/material/Autocomplete";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { format } from "date-fns";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import { Inter } from "next/font/google";
const inter = Inter({ subsets: ["latin"] });

const Tareas = () => {
  const [estadoModal, setEstadoModal] = useState<"add" | "update">("add");
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [rows, setRows] = useState<any[]>([]);
  const [filteredRows, setFilteredRows] = useState<any[]>([]);
  const [isSearchBarOpen, setIsSearchBarOpen] = useState(false);
  const [propietarios, setPropietarios] = useState<any[]>([]);
  const [establecimientos, setEstablecimientos] = useState<any[]>([]);
  const [error, setError] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    taskTitulo: "",
    taskDescripcion: "",
    taskEstablecimiento: "",
    taskFechaVencimiento: "",
    taskEstado: "",
  });

  const clearForm = () => {
    setFormData({
      taskTitulo: "",
      taskDescripcion: "",
      taskEstablecimiento: "",
      taskFechaVencimiento: "",
      taskEstado: "",
    });
    setError({});
  };

  const handleOpenAdd = () => {
    setEstadoModal("add");
    clearForm();
    setOpen(true);
  };

  const handleSearchClick = () => {
    setIsSearchBarOpen(!isSearchBarOpen);
  };

  const handleClickClose = (
    _event: React.MouseEvent<HTMLElement>,
    reason?: string
  ) => {
    if (reason && reason === "backdropClick") return;
    setOpen(false);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  /* BUSCAR ESTABLECIMIENTO*/
  const handleSearchFarmButtonClick = () => {
    localStorage.setItem("tareasFormData", JSON.stringify(formData));
    window.location.href = "/establecimiento";
  };

  /*OBTENER ESTABLECIMIENTOS */
  const obtenerEstablecimientos = async () => {
    try {
      const response = await fetch("http://localhost:8080/api/establecimiento");
      if (!response.ok) {
        throw new Error("No se pudo obtener la lista de establecimientos");
      }
      const data = await response.json();
      if (Array.isArray(data)) {
        setPropietarios(data);
      } else {
        // console.error("Los datos obtenidos no son un array:", data);
      }
    } catch (error) {
      // console.error("Error al obtener establecimientos:", error);
    }
  };

  useEffect(() => {
    const selectedEstablecimientoStr = localStorage.getItem(
      "selectedEstablecimiento"
    );
    const savedFormDataStr = localStorage.getItem("tareasFormData");
    const selectedEstablecimiento = selectedEstablecimientoStr
      ? JSON.parse(selectedEstablecimientoStr)
      : null;
    const savedFormData = savedFormDataStr
      ? JSON.parse(savedFormDataStr)
      : null;

    if (savedFormData) {
      setFormData(savedFormData);
    }

    if (selectedEstablecimiento) {
      setFormData((prevFormData) => ({
        ...prevFormData,
        establecimiento: selectedEstablecimiento,
      }));
    }

    // Limpiar localStorage
    localStorage.removeItem("tareasFormData");
    localStorage.removeItem("selectedEstablecimiento");
  }, []);

  useEffect(() => {
    localStorage.removeItem("formData");
  }, []);

  useEffect(() => {
    obtenerEstablecimientos(); // Llamar a la función para obtener clientes cuando el componente se monte
  }, []);

  const fetchTareas = async () => {
    /*  try {
      const response = await fetch("http://localhost:8080/api/tarea");
      if (!response.ok) {
        throw new Error("Error al obtener las tareas");
      }
      const data = await response.json();
      setRows(data);
      setFilteredRows(data);
    } catch (error) {
      console.error("Error fetching tasks:", error);
      setRows([]);
      setFilteredRows([]);
    } */
  };

  useEffect(() => {
    fetchTareas();
  }, []);

  /*CREACION DE UN NUEVA TAREA*/
  const handleAddTareas = (): void => {};
  /*MODIFICAR TAREA PARA GAURDAR*/
  const handleUpdateTareas = (): void => {};

  const handleSearchTareas = (value: string): void => {
    setSearchTerm(value);
    const filtered = rows.filter(
      (task) =>
        task.titulo.toLowerCase().includes(value.toLowerCase()) ||
        task.descripcion.toLowerCase().includes(value.toLowerCase()) ||
        task.establecimiento.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredRows(filtered);
  };
  /*CLICK BOTON EDITAR(LAPIZ)*/
  const handleEditTareas = async (id: number) => {};
  /*ELIMINAR TAREA*/
  const handleDeleteTareas = async (id: number) => {};

  const labelStyles = {
    fontWeight: 600,
    color: "#333",
    marginBottom: "8px",
    display: "block",
    fontFamily: "Lexend, sans-serif", // Cambiado a Lexend
  };
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;

    // Validaciones específicas para cada campo
    if (name === "taskTitulo" || name === "taskDescripcion") {
      if (!/^[a-zA-ZÀ-ÿ0-9\s]*$/.test(value)) {
        setError((prevError) => ({
          ...prevError,
          [name]: "Solo se permiten letras, números y espacios",
        }));
        return;
      } else {
        setError((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    // Validación para establecimiento (cuando se selecciona manualmente)
    if (name === "taskEstablecimiento") {
      if (!value) {
        setError((prevError) => ({
          ...prevError,
          [name]: "Debe seleccionar un establecimiento",
        }));
      } else {
        setError((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    // Validación para fecha de vencimiento
    if (name === "taskFechaVencimiento") {
      const selectedDate = new Date(value);
      const today = new Date();

      if (selectedDate < today) {
        setError((prevError) => ({
          ...prevError,
          [name]: "La fecha no puede ser anterior a hoy",
        }));
      } else {
        setError((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    // Validación para estado
    if (name === "taskEstado") {
      if (!["pendiente", "en_proceso", "completado"].includes(value)) {
        setError((prevError) => ({
          ...prevError,
          [name]: "Debe seleccionar un estado válido",
        }));
      } else {
        setError((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          mb: 3,
          mt: 3,
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="div"
            sx={{
              fontWeight: "bold",
              fontFamily: "Lexend, sans-serif", // Cambiado a Lexend
            }}
          >
            Tareas Laborales
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              color: "text.secondary",
              mt: 1,
              fontFamily: `${inter.style.fontFamily}`, // Cambiado a Inter
            }}
          >
            Gestione sus Tareas Laborales
          </Typography>
        </Box>
        <Button
          variant="contained"
          onClick={handleOpenAdd}
          sx={{
            bgcolor: "#2E7D32", // Color verde más oscuro
            color: "#ffffff",
            "&:hover": { bgcolor: "#0D9A0A" },
            height: "fit-content",
            alignSelf: "center",
            fontFamily: `${inter.style.fontFamily}`, // Agregado para usar Inter
          }}
          startIcon={<AddOutlinedIcon />}
        >
          Nueva Tarea
        </Button>
      </Box>

      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 3, // Espacio entre los Paper
        }}
      >
        <Paper
          elevation={2}
          sx={{
            p: 2,
            borderRadius: 2,
            width: "100%",
            maxWidth: "calc(100% - 32px)", // Resta el padding del contenedor
          }}
        >
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Buscar..."
            value={searchTerm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleSearchTareas(e.target.value)
            }
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton onClick={handleSearchClick}>
                    <SearchIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Paper>

        <Paper
          elevation={2}
          sx={{
            p: 2,
            borderRadius: 2,
            width: "100%",
            maxWidth: "calc(100% - 32px)", // Resta el padding del contenedor
          }}
        >
          {filteredRows.length > 0 ? (
            <Box sx={{ width: "100%" }}>
              {filteredRows.map((task) => (
                <Card
                  key={task.id!}
                  sx={{
                    mb: 2,
                    borderRadius: 2,
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    "&:hover": {
                      boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
                    },
                    "&:last-child": {
                      mb: 0,
                    },
                    width: "100%",
                  }}
                >
                  <CardContent>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "flex-start",
                      }}
                    >
                      <Box>
                        <Typography variant="h6" component="div">
                          {task.titulo}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ mt: 1 }}
                        >
                          Finca: {task.establecimiento}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {task.descripcion}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Vence:{" "}
                          {task.fechaVencimiento
                            ? format(
                                new Date(task.fechaVencimiento),
                                "dd/MM/yyyy"
                              )
                            : "No establecida"}
                        </Typography>
                      </Box>
                      <Box
                        sx={{ display: "flex", gap: 1, alignItems: "center" }}
                      >
                        <Chip
                          label={
                            task.estado === "pendiente"
                              ? "Pendiente"
                              : task.estado === "en_proceso"
                              ? "En Proceso"
                              : "Completado"
                          }
                          color={
                            task.estado === "pendiente"
                              ? "primary"
                              : task.estado === "en_proceso"
                              ? "warning"
                              : "success"
                          }
                          size="small"
                          sx={{ mr: 2 }}
                        />
                        <IconButton
                          size="small"
                          onClick={() => handleEditTareas(task.id)}
                          sx={{ color: "primary.main" }}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteTareas(task.id)}
                          sx={{ color: "error.main" }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Box>
          ) : (
            <Box
              sx={{
                width: "100%",
                textAlign: "center",
                py: 1, // Reduce el padding vertical
                bgcolor: "#f5f5f5",
                borderRadius: 2,
              }}
            >
              <Typography variant="h6" color="text.secondary">
                No hay tareas registradas
              </Typography>
            </Box>
          )}
        </Paper>
      </Box>

      <Dialog
        open={open}
        onClose={handleClickClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "12px",
            padding: "16px",
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <DialogTitle
              sx={{
                p: 0,
                fontFamily: "Lexend, sans-serif",
                fontSize: "1.5rem",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Registrar nueva tarea
            </DialogTitle>
            <DialogContentText
              sx={{
                p: 0,
                mt: 1,
                fontFamily: "Inter, sans-serif",
                color: "#666",
              }}
            >
              Complete la información de la nueva tarea a registrar.
            </DialogContentText>
            <IconButton
              aria-label="close"
              onClick={(event) => handleClickClose(event, "closeButtonClick")}
              sx={{ position: "absolute", right: 8, top: 8 }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <DialogContent sx={{ p: 0, px: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={labelStyles}>
              Título
            </Typography>
            <TextField
              placeholder="Ej: Visitar establecimiento Don Juan"
              variant="outlined"
              id="identificacion"
              name="taskTitulo"
              error={Boolean(error.taskTitulo)}
              helperText={error.taskTitulo}
              onChange={(
                e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
              ) => handleInputChange(e as React.ChangeEvent<HTMLInputElement>)}
              sx={{
                mb: 2,
                "& .MuiOutlinedInput-root": {
                  borderRadius: "8px",
                },
              }}
              value={formData.taskTitulo}
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {formData.taskTitulo &&
                      (error.taskTitulo ? (
                        <ErrorIcon color="error" />
                      ) : (
                        <CheckCircleIcon color="success" />
                      ))}
                  </InputAdornment>
                ),
                style: { fontFamily: "Inter, sans-serif" },
              }}
            />
            <Typography variant="body2" sx={labelStyles}>
              Descripción
            </Typography>
            <TextField
              placeholder="Ej: Visitar establecimiento Don Juan para realizar una fumigacion a la parcela A123"
              variant="outlined"
              id="identificacion"
              name="taskDescripcion"
              error={Boolean(error.taskDescripcion)}
              helperText={error.taskDescripcion}
              multiline
              rows={4}
              onChange={(
                e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
              ) => handleInputChange(e as React.ChangeEvent<HTMLInputElement>)}
              sx={{
                mb: 2,
                "& .MuiOutlinedInput-root": {
                  borderRadius: "8px",
                },
              }}
              value={formData.taskDescripcion}
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {formData.taskTitulo &&
                      (error.taskTitulo ? (
                        <ErrorIcon color="error" />
                      ) : (
                        <CheckCircleIcon color="success" />
                      ))}
                  </InputAdornment>
                ),
                style: { fontFamily: "Inter, sans-serif" },
              }}
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={labelStyles}>
              Ubicación
            </Typography>
            <Grid container spacing={2}>
              <Grid size={{ xs: 9 }}>
                <Autocomplete
                  value={formData.taskEstablecimiento}
                  readOnly={true}
                  disableClearable
                  options={establecimientos}
                  getOptionLabel={(option) => option?.nombre || ""}
                  onClick={handleSearchFarmButtonClick}
                  sx={{
                    "& .MuiAutocomplete-inputRoot": {
                      borderRadius: "8px",
                    },
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      id="establecimiento"
                      name="taskEstablecimiento"
                      placeholder="Ej: Don Juan"
                      variant="outlined"
                      error={Boolean(error.taskEstablecimiento)}
                      helperText={error.taskEstablecimiento}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <InputAdornment position="end">
                            {formData.taskEstablecimiento &&
                              typeof formData.taskEstablecimiento ===
                                "object" &&
                              formData.taskEstablecimiento !== null &&
                              "id" in
                                (formData.taskEstablecimiento as object) &&
                              !error.establecimiento && (
                                <CheckCircleIcon color="success" />
                              )}
                            {error.establecimiento && (
                              <ErrorIcon color="error" />
                            )}
                          </InputAdornment>
                        ),
                        style: { fontFamily: "Inter, sans-serif" },
                      }}
                      value={formData.taskEstablecimiento}
                      fullWidth
                      required
                    />
                  )}
                />
              </Grid>
              <Grid size={{ xs: 3 }}>
                <Button
                  variant="contained"
                  onClick={handleSearchFarmButtonClick}
                  startIcon={<SearchIcon />}
                  sx={{
                    height: "40px", // Altura reducida para igualar al botón Registrar
                    bgcolor: "#1976d2",
                    fontFamily: "Inter, sans-serif",
                    marginTop: "8px", // Alineación vertical con el botón Registrar
                  }}
                >
                  Buscar
                </Button>
              </Grid>
            </Grid>
          </Box>

          <Box>
            <Typography variant="body2" sx={labelStyles}>
              Programación y Estado
            </Typography>
            <Grid container spacing={2}>
              <Grid size={{ xs: 6 }}>
                <TextField
                  id="fechaVencimiento"
                  name="taskFechaVencimiento"
                  error={Boolean(error.taskFechaVencimiento)}
                  helperText={error.taskFechaVencimiento}
                  onChange={(
                    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
                  ) =>
                    handleInputChange(e as React.ChangeEvent<HTMLInputElement>)
                  }
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        {formData.taskTitulo &&
                          (error.taskTitulo ? (
                            <ErrorIcon color="error" />
                          ) : (
                            <CheckCircleIcon color="success" />
                          ))}
                      </InputAdornment>
                    ),
                    style: { fontFamily: "Inter, sans-serif" },
                  }}
                  value={formData.taskFechaVencimiento}
                  fullWidth
                  type="date"
                  InputLabelProps={{
                    shrink: true,
                  }}
                  placeholder="dd/mm/aaaa"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "8px",
                    },
                  }}
                />
              </Grid>
              <Grid size={{ xs: 6 }}>
                <FormControl fullWidth>
                  <InputLabel>Estado</InputLabel>
                  <Select
                    name="taskEstado"
                    value={formData.taskEstado}
                    onChange={(event: SelectChangeEvent<string>) => {
                      setFormData((prev) => ({
                        ...prev,
                        taskEstado: event.target.value,
                      }));
                    }}
                    displayEmpty
                    defaultValue="pendiente"
                    sx={{
                      borderRadius: "8px",
                    }}
                    style={{ fontFamily: "Inter, sans-serif" }}
                  >
                    <MenuItem
                      value="pendiente"
                      style={{ fontFamily: "Inter, sans-serif" }}
                    >
                      Pendiente
                    </MenuItem>
                    <MenuItem
                      value="en_proceso"
                      style={{ fontFamily: "Inter, sans-serif" }}
                    >
                      En Proceso
                    </MenuItem>
                    <MenuItem
                      value="completada"
                      style={{ fontFamily: "Inter, sans-serif" }}
                    >
                      Completada
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions
          sx={{
            padding: "16px 24px 24px 24px",
            justifyContent: "flex-end",
          }}
        >
          <Button
            type="submit"
            variant="contained"
            startIcon={<AddCircleIcon />}
            sx={{
              bgcolor: "#2E7D32",
              color: "#ffffff",
              "&:hover": { bgcolor: "#1B5E20" },
              textTransform: "none",
              "& .MuiSvgIcon-root": {
                color: "#ffffff",
              },
            }}
          >
            {estadoModal === "add" ? "Registrar" : "Guardar"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
export default Tareas;
